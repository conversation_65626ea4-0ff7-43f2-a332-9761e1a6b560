/*
 * Command Processor
 * 
 * Handles device commands and acknowledgments:
 * - reboot, suspend, resume, uimsg, config_reset, status
 * - Command acknowledgments with timestamps
 * - UI message display management
 */

#include <Arduino.h>
#include "config.h"
#include <ArduinoJson.h>
#include <WiFi.h>
#include <WiFiUdp.h>
#include <NTPClient.h>
#include <EEPROM.h>

#if HAS_OLED
  #include <Adafruit_GFX.h>
  #include <Adafruit_SSD1306.h>
#endif

// External references
extern device_config_t config;
extern String deviceId;
extern NTPClient timeClient;

#if HAS_OLED
extern Adafruit_SSD1306 display;
#endif

// Function prototypes
bool publishMessage(const char* topic, const char* payload, bool retain = false);
String getCommandAckTopic();
void eraseEEPROM();
void resetConfigurationToDefaults();
bool processConfigurationMessage(const String& payload);

// Forward declaration of sendCommandAck
bool sendCommandAck(const String& command, bool success, const String& message = "");

#if HAS_OLED
void displayWrappedText(const String& text, int x, int y, int maxCharsPerLine, int maxLines);
#endif

// Command state variables
static bool suspendMode = false;
static String suspendMessage = "";
static unsigned long uiMessageEndTime = 0;
static String uiMessage = "";
static bool uiMessageActive = false;

bool processCommandMessage(const String& payload) {
  DEBUG_PRINTF("Processing command: %s\n", payload.c_str());
  
  StaticJsonDocument<512> doc;
  DeserializationError error = deserializeJson(doc, payload);
  
  if (error) {
    DEBUG_PRINTF("Command JSON parsing failed: %s\n", error.c_str());
    sendCommandAck("unknown", false, "JSON parsing error");
    return false;
  }
  
  if (!doc.containsKey("command")) {
    sendCommandAck("unknown", false, "Missing command field");
    return false;
  }
  
  String command = doc["command"].as<String>();
  
  // Process commands
  if (command == "reboot") {
    sendCommandAck(command, true, "Rebooting device");
    delay(1000);
    ESP.restart();
    
  } else if (command == "suspend") {
    String message = doc.containsKey("message") ? doc["message"].as<String>() : "Device suspended";
    int timeout = doc.containsKey("timeout") ? doc["timeout"].as<int>() : 0;
    
    suspendMode = true;
    suspendMessage = message;
    
    sendCommandAck(command, true, "Device suspended");
    DEBUG_PRINTF("Device suspended with message: %s\n", message.c_str());
    
  } else if (command == "resume") {
    suspendMode = false;
    suspendMessage = "";
    sendCommandAck(command, true, "Device resumed");
    DEBUG_PRINTLN("Device resumed");
    
  } else if (command == "uimsg") {
    if (doc.containsKey("message") && doc.containsKey("duration")) {
      String message = doc["message"].as<String>();
      int duration = doc["duration"].as<int>();
      
      uiMessage = message;
      uiMessageActive = true;
      
      if (duration > 0) {
        uiMessageEndTime = millis() + (duration * 1000);
      } else {
        uiMessageEndTime = 0; // Display until power cycle
      }
      
      sendCommandAck(command, true, "UI message displayed");
      DEBUG_PRINTF("UI message set: %s (duration: %d)\n", message.c_str(), duration);
      
    } else {
      sendCommandAck(command, false, "Missing message or duration");
    }
    
  } else if (command == "config_reset") {
    sendCommandAck(command, true, "Resetting configuration");
    
    DEBUG_PRINTLN("Performing configuration reset...");
    eraseEEPROM();
    
    delay(1000);
    ESP.restart();
    
  } else if (command == "status") {
    // Return current device status
    StaticJsonDocument<512> statusDoc;
    statusDoc["device_id"] = deviceId;
    statusDoc["status_code"] = config.status_code;
    statusDoc["uptime"] = millis() / 1000;
    statusDoc["heap"] = ESP.getFreeHeap();
    statusDoc["wifi_rssi"] = WiFi.RSSI();
    statusDoc["wifi_ip"] = WiFi.localIP().toString();
    statusDoc["suspend_mode"] = suspendMode;
    statusDoc["ui_message_active"] = uiMessageActive;
    statusDoc["config_version"] = config.config_version;
    statusDoc["config_crc"] = config.config_crc;
    statusDoc["firmware"] = config.firmware_version;
    statusDoc["hardware"] = config.hardware_version;
    
    String statusPayload;
    serializeJson(statusDoc, statusPayload);
    
    sendCommandAck(command, true, statusPayload);
    
  } else {
    sendCommandAck(command, false, "Unknown command");
    return false;
  }
  
  return true;
}

bool sendCommandAck(const String& command, bool success, const String& message) {
  DEBUG_PRINTF("Sending command ack: %s -> %s\n", command.c_str(), success ? "SUCCESS" : "FAILED");
  
  StaticJsonDocument<512> doc;
  doc["device_id"] = deviceId;
  doc["command"] = command;
  doc["success"] = success;
  doc["timestamp"] = timeClient.getEpochTime();
  doc["message"] = message;
  doc["uptime"] = millis() / 1000;
  
  String payload;
  serializeJson(doc, payload);
  
  String ackTopic = getCommandAckTopic();
  return publishMessage(ackTopic.c_str(), payload.c_str());
}

bool isInSuspendMode() {
  return suspendMode;
}

String getSuspendMessage() {
  return suspendMessage;
}

String getCurrentUIMessage() {
  if (!uiMessageActive) {
    return "";
  }
  
  // Check if message has expired
  if (uiMessageEndTime > 0 && millis() >= uiMessageEndTime) {
    uiMessage = "";
    uiMessageActive = false;
    uiMessageEndTime = 0;
    return "";
  }
  
  return uiMessage;
}

bool hasActiveUIMessage() {
  return uiMessageActive && getCurrentUIMessage().length() > 0;
}

void clearUIMessage() {
  uiMessage = "";
  uiMessageActive = false;
  uiMessageEndTime = 0;
}

void updateDisplayWithMessages() {
  #if HAS_OLED
  String currentMsg = getCurrentUIMessage();
  String suspendMsg = getSuspendMessage();
  
  // Priority: UI message > Suspend message > Normal display
  if (currentMsg.length() > 0) {
    // Display UI message
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("MESSAGE:");
    display.setCursor(0, 15);
    
    // Word wrap the message
    displayWrappedText(currentMsg, 0, 15, 21, 4); // 21 chars per line, 4 lines max
    
    display.display();
    return;
  }
  
  if (suspendMode && suspendMsg.length() > 0) {
    // Display suspend message
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("SUSPENDED");
    display.setCursor(0, 15);
    
    // Word wrap the suspend message
    displayWrappedText(suspendMsg, 0, 15, 21, 4); // 21 chars per line, 4 lines max
    
    display.display();
    return;
  }
  #endif
}

#if HAS_OLED
void displayWrappedText(const String& text, int x, int y, int maxCharsPerLine, int maxLines) {
  int currentLine = 0;
  int startIndex = 0;
  int lineHeight = 10;
  
  while (startIndex < text.length() && currentLine < maxLines) {
    int endIndex = startIndex + maxCharsPerLine;
    
    // Don't break words if possible
    if (endIndex < text.length()) {
      int lastSpace = text.lastIndexOf(' ', endIndex);
      if (lastSpace > startIndex) {
        endIndex = lastSpace;
      }
    } else {
      endIndex = text.length();
    }
    
    String line = text.substring(startIndex, endIndex);
    display.setCursor(x, y + (currentLine * lineHeight));
    display.println(line);
    
    startIndex = endIndex;
    if (startIndex < text.length() && text.charAt(startIndex) == ' ') {
      startIndex++; // Skip the space
    }
    
    currentLine++;
  }
}
#endif

bool shouldPublishTelemetry() {
  // Don't publish telemetry if suspended
  return !suspendMode;
}

void printCommandStatus() {
  DEBUG_PRINTLN("\n===== COMMAND STATUS =====");
  DEBUG_PRINTF("Suspend mode: %s\n", suspendMode ? "Yes" : "No");
  if (suspendMode) {
    DEBUG_PRINTF("Suspend message: %s\n", suspendMessage.c_str());
  }
  DEBUG_PRINTF("UI message active: %s\n", uiMessageActive ? "Yes" : "No");
  if (uiMessageActive) {
    DEBUG_PRINTF("UI message: %s\n", uiMessage.c_str());
    if (uiMessageEndTime > 0) {
      unsigned long remaining = (uiMessageEndTime > millis()) ? 
                               (uiMessageEndTime - millis()) / 1000 : 0;
      DEBUG_PRINTF("Time remaining: %lu seconds\n", remaining);
    } else {
      DEBUG_PRINTLN("Time remaining: indefinite");
    }
  }
  DEBUG_PRINTLN("==========================\n");
}

// MQTT callback function
void mqttMessageCallback(char* topic, byte* payload, unsigned int length) {
  // Convert payload to string
  String message;
  for (unsigned int i = 0; i < length; i++) {
    message += (char)payload[i];
  }
  
  String topicStr = String(topic);
  DEBUG_PRINTF("MQTT message received [%s]: %s\n", topic, message.c_str());
  
  // Determine message type based on topic
  if (topicStr.indexOf("/config") >= 0 || topicStr.startsWith("config/")) {
    // Configuration message
    processConfigurationMessage(message);
  } else if (topicStr.indexOf("/command") >= 0 || topicStr.startsWith("command/")) {
    // Command message
    processCommandMessage(message);
  } else {
    DEBUG_PRINTF("Unknown topic pattern: %s\n", topic);
  }
}

// Forward declaration for config processing
bool processConfigurationMessage(const String& payload);
