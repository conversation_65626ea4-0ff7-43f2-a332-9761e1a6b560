/*
 * ESP32 Sensor Command Processing
 * 
 * Handles device commands and acknowledgments
 */

#include <Arduino.h>
#include "config.h"
#include <ArduinoJson.h>
#include <PubSubClient.h>

// External references
extern device_config_t config;
extern String deviceId;
extern PubSubClient* currentMqtt;
extern NTPClient timeClient;

#if HAS_OLED
extern Adafruit_SSD1306 display;
#endif

// Command state variables
bool suspendMode = false;
String suspendMessage = "";
unsigned long uiMessageEndTime = 0;
String uiMessage = "";

void processCommandMessage(const String& payload) {
  DEBUG_PRINTF("Processing command: %s\n", payload.c_str());
  
  StaticJsonDocument<512> doc;
  DeserializationError error = deserializeJson(doc, payload);
  
  if (error) {
    DEBUG_PRINTF("Command JSON parsing failed: %s\n", error.c_str());
    sendCommandAck("unknown", false, "JSON parsing error");
    return;
  }
  
  if (!doc.containsKey("command")) {
    sendCommandAck("unknown", false, "Missing command field");
    return;
  }
  
  String command = doc["command"].as<String>();
  
  if (command == "reboot") {
    sendCommandAck(command, true, "Rebooting device");
    delay(1000);
    ESP.restart();
    
  } else if (command == "suspend") {
    String message = doc.containsKey("message") ? doc["message"].as<String>() : "Device suspended";
    suspendMode = true;
    suspendMessage = message;
    sendCommandAck(command, true, "Device suspended");
    
  } else if (command == "resume") {
    suspendMode = false;
    suspendMessage = "";
    sendCommandAck(command, true, "Device resumed");
    
  } else if (command == "uimsg") {
    if (doc.containsKey("message") && doc.containsKey("duration")) {
      String message = doc["message"].as<String>();
      int duration = doc["duration"].as<int>();
      
      uiMessage = message;
      if (duration > 0) {
        uiMessageEndTime = millis() + (duration * 1000);
      } else {
        uiMessageEndTime = 0; // Display until power cycle
      }
      
      sendCommandAck(command, true, "UI message displayed");
    } else {
      sendCommandAck(command, false, "Missing message or duration");
    }
    
  } else if (command == "config_reset") {
    sendCommandAck(command, true, "Resetting configuration");
    
    // Clear EEPROM
    for (int i = 0; i < EEPROM_SIZE; i++) {
      EEPROM.write(i, 0xFF);
    }
    EEPROM.commit();
    
    delay(1000);
    ESP.restart();
    
  } else if (command == "status") {
    // Return current device status
    StaticJsonDocument<512> statusDoc;
    statusDoc["device_id"] = deviceId;
    statusDoc["status_code"] = config.status_code;
    statusDoc["uptime"] = millis() / 1000;
    statusDoc["heap"] = ESP.getFreeHeap();
    statusDoc["wifi_rssi"] = WiFi.RSSI();
    statusDoc["suspend_mode"] = suspendMode;
    
    String statusPayload;
    serializeJson(statusDoc, statusPayload);
    
    sendCommandAck(command, true, statusPayload);
    
  } else {
    sendCommandAck(command, false, "Unknown command");
  }
}

void sendCommandAck(const String& command, bool success, const String& message) {
  if (!currentMqtt || !currentMqtt->connected()) return;
  
  StaticJsonDocument<512> doc;
  doc["device_id"] = deviceId;
  doc["command"] = command;
  doc["success"] = success;
  doc["timestamp"] = timeClient.getEpochTime();
  doc["message"] = message;
  
  String payload;
  serializeJson(doc, payload);
  
  // Determine command ack topic based on mode
  String ackTopic;
  if (config.status_code == STATUS_ADULT || config.status_code == STATUS_POC) {
    ackTopic = String(POC_COMMAND_PREFIX) + deviceId + "/ack";
  } else {
    ackTopic = String(COMMAND_ACK_TOPIC_PREFIX) + deviceId;
  }
  
  if (currentMqtt->publish(ackTopic.c_str(), payload.c_str())) {
    DEBUG_PRINTF("Command ack sent to: %s\n", ackTopic.c_str());
    DEBUG_PRINTF("Ack payload: %s\n", payload.c_str());
  } else {
    DEBUG_PRINTLN("Failed to send command ack!");
  }
}

void mqttCallback(char* topic, byte* payload, unsigned int length) {
  // Convert payload to string
  String message;
  for (unsigned int i = 0; i < length; i++) {
    message += (char)payload[i];
  }
  
  String topicStr = String(topic);
  DEBUG_PRINTF("MQTT message received [%s]: %s\n", topic, message.c_str());
  
  // Determine message type based on topic
  if (topicStr.indexOf("/config") >= 0 || topicStr.startsWith("config/")) {
    processConfigMessage(message);
  } else if (topicStr.indexOf("/command") >= 0 || topicStr.startsWith("command/")) {
    processCommandMessage(message);
  } else {
    DEBUG_PRINTF("Unknown topic: %s\n", topic);
  }
}

bool isInSuspendMode() {
  return suspendMode;
}

String getSuspendMessage() {
  return suspendMessage;
}

String getCurrentUIMessage() {
  if (uiMessage.length() > 0) {
    if (uiMessageEndTime == 0 || millis() < uiMessageEndTime) {
      return uiMessage;
    } else {
      // Message expired
      uiMessage = "";
      uiMessageEndTime = 0;
    }
  }
  return "";
}

void updateDisplayWithMessages() {
  #if HAS_OLED
  String currentMsg = getCurrentUIMessage();
  String suspendMsg = getSuspendMessage();
  
  if (currentMsg.length() > 0) {
    // Display UI message
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    
    // Word wrap the message
    int lineHeight = 10;
    int maxCharsPerLine = 21; // Approximate for 128px width
    int currentLine = 0;
    
    for (int i = 0; i < currentMsg.length() && currentLine < 6; i += maxCharsPerLine) {
      String line = currentMsg.substring(i, min(i + maxCharsPerLine, (int)currentMsg.length()));
      display.setCursor(0, currentLine * lineHeight);
      display.println(line);
      currentLine++;
    }
    
    display.display();
    return;
  }
  
  if (suspendMode && suspendMsg.length() > 0) {
    // Display suspend message
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("SUSPENDED");
    display.setCursor(0, 20);
    
    // Word wrap the suspend message
    int lineHeight = 10;
    int maxCharsPerLine = 21;
    int currentLine = 2; // Start after "SUSPENDED"
    
    for (int i = 0; i < suspendMsg.length() && currentLine < 6; i += maxCharsPerLine) {
      String line = suspendMsg.substring(i, min(i + maxCharsPerLine, (int)suspendMsg.length()));
      display.setCursor(0, currentLine * lineHeight);
      display.println(line);
      currentLine++;
    }
    
    display.display();
    return;
  }
  #endif
}
