/*
 * ESP32 Sensor Provisioning Functions
 * 
 * Handles device provisioning, MQTT communication, and lifecycle management
 */

#include <Arduino.h>
#include "config.h"
#include <ArduinoJson.h>
#include <PubSubClient.h>

// External references to global variables
extern device_config_t config;
extern String deviceId;
extern String macAddress;
extern PubSubClient* currentMqtt;
extern NTPClient timeClient;
extern SensorData currentSensorData;
extern bool configReceived;
extern uint8_t connectionFailures;

// Function prototypes
void saveConfiguration();
void transitionToStatus(device_status_t newStatus);

void connectMQTT() {
  if (!currentMqtt) return;
  
  DEBUG_PRINTF("Attempting MQTT connection to %s:%d\n", config.mqtt_endpoint, config.mqtt_port);
  DEBUG_PRINTF("Client ID: %s\n", config.mqtt_client_id);
  
  bool connected = false;
  
  if (strlen(config.mqtt_username) > 0) {
    // Connect with username/password (provisioning mode)
    connected = currentMqtt->connect(config.mqtt_client_id, config.mqtt_username, config.mqtt_password);
  } else {
    // Connect with certificates only (AWS IoT mode)
    connected = currentMqtt->connect(config.mqtt_client_id);
  }
  
  if (connected) {
    DEBUG_PRINTLN("MQTT connected!");
    connectionFailures = 0;
    
    // Subscribe to appropriate topics based on status
    String configTopic, commandTopic;
    
    if (config.status_code == STATUS_ADULT || config.status_code == STATUS_POC) {
      // Adult/POC mode: subscribe to operational topics
      configTopic = String(POC_CONFIG_PREFIX) + deviceId + CONFIG_SUFFIX;
      commandTopic = String(POC_COMMAND_PREFIX) + deviceId + COMMAND_SUFFIX;
    } else {
      // Provisioning mode: subscribe to provisioning topics
      configTopic = String(CONFIG_TOPIC_PREFIX) + deviceId;
      commandTopic = String(COMMAND_TOPIC_PREFIX) + deviceId;
    }
    
    currentMqtt->subscribe(configTopic.c_str());
    currentMqtt->subscribe(commandTopic.c_str());
    
    DEBUG_PRINTF("Subscribed to config: %s\n", configTopic.c_str());
    DEBUG_PRINTF("Subscribed to command: %s\n", commandTopic.c_str());
    
  } else {
    DEBUG_PRINTF("MQTT connection failed. State: %d\n", currentMqtt->state());
    handleConnectionFailure();
  }
}

void handleConnectionFailure() {
  connectionFailures++;
  DEBUG_PRINTF("Connection failure count: %d\n", connectionFailures);
  
  if (connectionFailures >= config.max_connection_failures) {
    DEBUG_PRINTLN("Max connection failures reached. Transitioning to fallback mode...");
    
    if (config.status_code == STATUS_ADULT) {
      // Adult failed to connect to AWS, try provisioning
      transitionToStatus(STATUS_GEEZER);
    } else if (config.status_code == STATUS_GEEZER) {
      // Geezer failed to connect to provisioning, stay in geezer mode
      DEBUG_PRINTLN("Staying in geezer mode, will retry later...");
    }
    
    connectionFailures = 0; // Reset counter
  }
}

void publishHello() {
  if (!currentMqtt || !currentMqtt->connected()) return;
  
  DEBUG_PRINTLN("Publishing hello message...");
  
  // Create hello JSON
  StaticJsonDocument<JSON_BUFFER_SIZE> doc;
  doc["deviceID"] = deviceId;
  doc["statusCode"] = config.status_code;
  doc["deviceType"] = config.device_type;
  doc["firmwareVersion"] = config.firmware_version;
  doc["hardwareVersion"] = config.hardware_version;
  doc["macAddress"] = macAddress;
  doc["timestamp"] = timeClient.getEpochTime();
  doc["rssi"] = WiFi.RSSI();
  doc["heap"] = ESP.getFreeHeap();
  
  // Add notes based on status
  switch (config.status_code) {
    case STATUS_NEWBORN:
      doc["notes"] = "New device requesting provisioning";
      break;
    case STATUS_TODDLER:
      doc["notes"] = "Partial configuration received";
      break;
    case STATUS_TEENAGER:
      doc["notes"] = "Configuration complete, requesting verification";
      break;
    case STATUS_GEEZER:
      doc["notes"] = "Credentials expired, requesting renewal";
      break;
    default:
      doc["notes"] = "Status update";
      break;
  }
  
  String payload;
  serializeJson(doc, payload);
  
  // Publish to hello topic
  String helloTopic = String(HELLO_TOPIC_PREFIX) + deviceId;
  
  if (currentMqtt->publish(helloTopic.c_str(), payload.c_str())) {
    DEBUG_PRINTF("Hello published to: %s\n", helloTopic.c_str());
    DEBUG_PRINTF("Payload: %s\n", payload.c_str());
  } else {
    DEBUG_PRINTLN("Failed to publish hello!");
  }
}

void publishTelemetry() {
  if (!currentMqtt || !currentMqtt->connected()) return;
  
  DEBUG_PRINTLN("Publishing telemetry...");
  
  // Create telemetry JSON
  StaticJsonDocument<JSON_BUFFER_SIZE> doc;
  doc["device_id"] = deviceId;
  doc["timestamp"] = currentSensorData.timestamp;
  doc["temperature"] = currentSensorData.temperature;
  doc["humidity"] = currentSensorData.humidity;
  doc["adc_voltage"] = currentSensorData.adcVoltage;
  doc["binary_state"] = currentSensorData.binaryState;
  doc["connection"] = currentSensorData.connectionType;
  doc["rssi"] = WiFi.RSSI();
  doc["heap"] = ESP.getFreeHeap();
  doc["uptime"] = millis() / 1000;
  
  String payload;
  serializeJson(doc, payload);
  
  // Determine telemetry topic based on mode
  String telemetryTopic;
  if (config.status_code == STATUS_POC) {
    telemetryTopic = String(POC_TELEMETRY_PREFIX) + deviceId + TELEMETRY_SUFFIX;
  } else {
    telemetryTopic = String(POC_TELEMETRY_PREFIX) + TELEMETRY_SUFFIX;
  }
  
  if (currentMqtt->publish(telemetryTopic.c_str(), payload.c_str())) {
    DEBUG_PRINTF("Telemetry published to: %s\n", telemetryTopic.c_str());
    DEBUG_PRINTF("Payload: %s\n", payload.c_str());
  } else {
    DEBUG_PRINTLN("Failed to publish telemetry!");
  }
}

void processConfigMessage(const String& payload) {
  DEBUG_PRINTF("Processing config message: %s\n", payload.c_str());
  
  StaticJsonDocument<2048> doc;
  DeserializationError error = deserializeJson(doc, payload);
  
  if (error) {
    DEBUG_PRINTF("JSON parsing failed: %s\n", error.c_str());
    return;
  }
  
  // Store original config for comparison
  device_config_t originalConfig = config;
  bool configChanged = false;
  
  // Process each field in the JSON
  if (doc.containsKey("wifi_ssid")) {
    String newSsid = doc["wifi_ssid"].as<String>();
    if (newSsid != String(config.wifi_ssid)) {
      strncpy(config.wifi_ssid, newSsid.c_str(), sizeof(config.wifi_ssid) - 1);
      configChanged = true;
    }
  }
  
  if (doc.containsKey("wifi_pass")) {
    String newPass = doc["wifi_pass"].as<String>();
    if (newPass != String(config.wifi_pass)) {
      strncpy(config.wifi_pass, newPass.c_str(), sizeof(config.wifi_pass) - 1);
      configChanged = true;
    }
  }
  
  if (doc.containsKey("mqtt_endpoint")) {
    String newEndpoint = doc["mqtt_endpoint"].as<String>();
    if (newEndpoint != String(config.mqtt_endpoint)) {
      strncpy(config.mqtt_endpoint, newEndpoint.c_str(), sizeof(config.mqtt_endpoint) - 1);
      configChanged = true;
    }
  }
  
  if (doc.containsKey("mqtt_port")) {
    uint16_t newPort = doc["mqtt_port"].as<uint16_t>();
    if (newPort != config.mqtt_port) {
      config.mqtt_port = newPort;
      configChanged = true;
    }
  }
  
  if (doc.containsKey("mqtt_cert")) {
    String newCert = doc["mqtt_cert"].as<String>();
    if (newCert != String(config.mqtt_cert)) {
      strncpy(config.mqtt_cert, newCert.c_str(), sizeof(config.mqtt_cert) - 1);
      configChanged = true;
    }
  }
  
  if (doc.containsKey("mqtt_key")) {
    String newKey = doc["mqtt_key"].as<String>();
    if (newKey != String(config.mqtt_key)) {
      strncpy(config.mqtt_key, newKey.c_str(), sizeof(config.mqtt_key) - 1);
      configChanged = true;
    }
  }
  
  if (doc.containsKey("mqtt_ca")) {
    String newCa = doc["mqtt_ca"].as<String>();
    if (newCa != String(config.mqtt_ca)) {
      strncpy(config.mqtt_ca, newCa.c_str(), sizeof(config.mqtt_ca) - 1);
      configChanged = true;
    }
  }
  
  if (doc.containsKey("publish_interval_sec")) {
    uint32_t newInterval = doc["publish_interval_sec"].as<uint32_t>();
    if (newInterval != config.publish_interval_sec) {
      config.publish_interval_sec = newInterval;
      configChanged = true;
    }
  }
  
  if (doc.containsKey("ui_name")) {
    String newName = doc["ui_name"].as<String>();
    if (newName != String(config.ui_name)) {
      strncpy(config.ui_name, newName.c_str(), sizeof(config.ui_name) - 1);
      configChanged = true;
    }
  }
  
  // Add more field processing as needed...
  
  if (configChanged) {
    DEBUG_PRINTLN("Configuration updated, saving to EEPROM...");
    saveConfiguration();
    configReceived = true;
    
    // Transition status based on current state
    if (config.status_code == STATUS_NEWBORN) {
      transitionToStatus(STATUS_TEENAGER);
    } else if (config.status_code == STATUS_GEEZER) {
      transitionToStatus(STATUS_ADULT);
    }
    
    // Publish updated hello
    publishHello();
    
    // Schedule reboot to apply new configuration
    DEBUG_PRINTLN("Rebooting in 5 seconds to apply new configuration...");
    delay(5000);
    ESP.restart();
  }
}

void transitionToStatus(device_status_t newStatus) {
  if (config.status_code != newStatus) {
    DEBUG_PRINTF("Status transition: %d -> %d\n", config.status_code, newStatus);
    config.status_code = newStatus;
    saveConfiguration();
  }
}
