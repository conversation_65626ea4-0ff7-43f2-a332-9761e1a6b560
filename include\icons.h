#pragma once
#include <Arduino.h>

const uint8_t wifi_icon_0[] PROGMEM = {
    0b11000000,
    0b11000000,
    0b11000000,
    0b10010001,
    0b10001010,
    0b10000100,
    0b10001010,
    0b10010001
  };
  
  const uint8_t wifi_icon_1[] PROGMEM = {
    0b11000000,
    0b11000000,
    0b11000000,
    0b10000000,
    0b10000000,
    0b10000000,
    0b10000010,
    0b10101010
  };
  
  const uint8_t wifi_icon_2[] PROGMEM = {
    0b11000000,
    0b11000000,
    0b11000000,
    0b10000000,
    0b10000000,
    0b10001010,
    0b10101010,
    0b10101010
  };
  
  const uint8_t wifi_icon_3[] PROGMEM = {
    0b11000000,
    0b11000000,
    0b11000000,
    0b10000010,
    0b10001010,
    0b10101010,
    0b10101010,
    0b10101010
  };
  
  const uint8_t wifi_icon_4[] PROGMEM = {
    0b11000000,
    0b11000000,
    0b11000001,
    0b11000101,
    0b10010101,
    0b10010101,
    0b10010101,
    0b10010101
  };
  
  const uint8_t wifi_icon_5[] PROGMEM = {
    0b11000000,
    0b11000001,
    0b11000101,
    0b10010101,
    0b10010101,
    0b10010101,
    0b10010101,
    0b10010101
  };
  
  const uint8_t wifi_icon_6[] PROGMEM = {
    0b11000001,
    0b11000101,
    0b11010101,
    0b10010101,
    0b10010101,
    0b10010101,
    0b10010101,
    0b10010101
  };
  const uint8_t checkmark_icon[8] PROGMEM = { 0x00, 0x01, 0x02, 0x04, 0x88, 0x50, 0x20, 0x00 };
  const uint8_t x_icon[8] PROGMEM = { 0x81, 0x42, 0x24, 0x18, 0x18, 0x24, 0x42, 0x81 };

  const uint8_t mqtt_icon[8] PROGMEM = { // Simple 'cloud' icon for MQTT
    0b00111100, 0b01111110, 0b11011011, 0b11011011,
    0b11111111, 0b01111110, 0b00111100, 0b00000000
  };
  const uint8_t sensor_icon[8] PROGMEM = { // Simple thermometer icon
    0b00011000, 0b00100100, 0b00100100, 0b00011000,
    0b00011000, 0b00111100, 0b01111110, 0b00111100
  };
