# ESP8266 AWS IoT Sensor - Version History

## File Descriptions

### `src/main.old`
- **Original version** with AWS IoT Core functionality
- Contains full AWS IoT MQTT over TLS/SSL implementation
- **Status**: Causes memory crashes and watchdog resets on ESP8266
- **Issue**: BearSSL TLS handshake too memory-intensive (~35KB heap insufficient)
- **Use**: Reference for AWS IoT implementation

### `src/main.stable.cpp` (RECOMMENDED)
- **Current stable version** - AWS IoT disabled for reliability
- **Status**: ✅ FULLY WORKING - Continuous operation without crashes
- **Features Working**:
  - AM2320 temperature/humidity sensor
  - ADC voltage reading (0-3.3V)
  - Digital binary inputs on D5/D6
  - SSD1306 OLED display with status icons
  - NTP time synchronization
  - WiFi connectivity with auto-reconnect
  - EEPROM configuration storage with corruption recovery
  - Serial telemetry output in JSON format
  - Watchdog timer management
- **Memory Usage**: ~35KB free heap (stable)
- **Output**: Comprehensive telemetry data via serial port every 60 seconds

### `src/main.cpp`
- **Current active version** (same as main.stable.cpp)
- This is what's currently running on the device

## Problem Analysis

### Root Cause
The ESP8266 has insufficient memory (~35KB free heap) to handle AWS IoT Core's TLS/SSL requirements:
- BearSSL library memory allocation
- Certificate validation overhead
- Complex cryptographic operations
- Large buffer requirements for TLS handshake

### Symptoms Observed
- Exception 28 (LoadProhibitedCause) - Memory access violation
- Exception 9 (LoadStoreAlignmentCause) - Memory alignment issue  
- Exception 4 (Soft WDT reset) - Watchdog timer timeout
- All crashes occurred during TLS handshake: "Starting TLS handshake..."

## Solutions Attempted
1. ❌ Reduced TLS buffer sizes (512→256 bytes)
2. ❌ Added watchdog feeding during connection
3. ❌ Implemented connection timeouts
4. ❌ Used different PubSubClient library methods
5. ✅ **Disabled AWS IoT for stable sensor operation**

## Recommendations

### For Current Setup (ESP8266)
- **Use the stable version** (`main.stable.cpp`) for reliable sensor operation
- **Collect data via serial output** - comprehensive JSON telemetry every 60 seconds
- **Consider local data logging** with SD card module
- **Use HTTP POST** for simpler cloud connectivity (much lower memory usage)

### For AWS IoT Connectivity
- **Upgrade to ESP32** - Has ~300KB+ free heap, easily handles AWS IoT Core
- **Use external gateway** - ESP8266 sends data to local server, which forwards to AWS
- **Use simplified MQTT** - Local broker without TLS (for local networks only)

## Current Telemetry Output Format

```
=== TELEMETRY DATA ===
Timestamp: 2025-06-24T00:38:18Z
NodeID: 00Zpj4
Temperature: 22.20 °C
Humidity: 57.90 %
Voltage: 1.72 V
Binary Input: 3
WiFi RSSI: -32 dBm
Free Heap: 35672 bytes
Uptime: 1234 seconds

JSON: {"nodeId":"00Zpj4","timestamp":"2025-06-24T00:38:18Z","temperature":22.20,"humidity":57.90,"voltage":1.72,"binary":3,"rssi":-32,"heap":35672}
======================
```

## Hardware Verified Working
- ✅ ESP8266 (NodeMCU/Wemos D1 Mini)
- ✅ AM2320 Temperature/Humidity Sensor (I2C)
- ✅ SSD1306 OLED Display 128x32 (I2C)
- ✅ Analog input (0-3.3V via voltage divider)
- ✅ Digital inputs D5/D6 (binary encoding)
- ✅ WiFi connectivity (2.4GHz)

## Next Steps
1. **Keep using stable version** for reliable sensor data collection
2. **Consider ESP32 upgrade** if AWS IoT Core connectivity is required
3. **Implement HTTP-based telemetry** as alternative to MQTT
4. **Add SD card logging** for local data storage
5. **Set up data collection server** to receive serial/HTTP data
