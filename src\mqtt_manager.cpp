/*
 * MQTT Manager
 * 
 * Manages dual MQTT connections:
 * - Provisioning MQ<PERSON> (insecure, for newborn/toddler/geezer)
 * - AWS IoT MQTT (secure, for teenager/adult/POC)
 */

#include <Arduino.h>
#include "config.h"
#include <WiFi.h>
#include <WiFiClient.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>

// External references
extern device_config_t config;
extern String deviceId;

// Function prototypes
void handleConnectionFailure();
void handleSuccessfulConnection();
bool isOperationalStatus();
bool isProvisioningStatus();
void mqttMessageCallback(char* topic, byte* payload, unsigned int length);

// MQTT clients
static WiFiClient provisioningWifiClient;
static WiFiClientSecure awsWifiClient;
static PubSubClient provisioningMqtt(provisioningWifiClient);
static PubSubClient awsMqtt(awsWifiClient);

// Current active client
static PubSubClient* currentMqtt = nullptr;
static bool isUsingAWS = false;

// Connection state
static bool mqttConnected = false;
static unsigned long lastConnectionAttempt = 0;

// AWS IoT certificates (embedded)
static const char aws_ca_cert[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
ADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6
b24gUm9vdCBDQSAxMB4XDTE1MDUyNjAwMDAwMFoXDTM4MDExNzAwMDAwMFowOTEL
MAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv
b3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj
ca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM
9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw
IFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6
VOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L
93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm
jgSubJrIqg0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0OBBYEFIQYzIU07LwMlJQuCFmcx7IQTgoIMA0GCSqGSIb3DQEBCwUA
A4IBAQCY8jdaQZChGsV2USggNiMOruYou6r4lK5IpDB/G/wkjUu0yKGX9rbxenDI
U5PMCCjjmCXPI6T53iHTfIUJrU6adTrCC2qJeHZERxhlbI1Bjjt/msv0tadQ1wUs
N+gDS63pYaACbvXy8MWy7Vu33PqUXHeeE6V/Uq2V8viTO96LXFvKWlJbYK8U90vv
o/ufQJVtMVT8QtPHRh8jrdkPSHCa2XV4cdFyQzR1bldZwgJcJmApzyMZFo6IQ6XU
5MsI+yMRQ+hDKXJioaldXgjUkK642M4UwtBV8ob2xJNDd2ZhwLnoQdeXeGADbkpy
rqXRfboQnoZsG4q5WTP468SQvvG5
-----END CERTIFICATE-----
)EOF";

static const char aws_device_cert[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDWTCCAkGgAwIBAgIUQLLrIO5d6G9m6/tgpMttQs9ncW0wDQYJKoZIhvcNAQEL
BQAwTTFLMEkGA1UECwxCQW1hem9uIFdlYiBTZXJ2aWNlcyBPPUFtYXpvbi5jb20g
SW5jLiBMPVNlYXR0bGUgU1Q9V2FzaGluZ3RvbiBDPVVTMB4XDTI1MDYxMzAwNTgx
NVoXDTQ5MTIzMTIzNTk1OVowHjEcMBoGA1UEAwwTQVdTIElvVCBDZXJ0aWZpY2F0
ZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKsW39KzechA5NkWWfAF
5H6sNeICnc9hjGjZQ3tI6VSHjDFhGNtnf+2eC6HQZ/m86jSOtmJnvYD0lBd/4cle
ZT/LLiDTRgyFL+GKnmgRN0KhNsvgVaR1tnTMY+AszAhpgBf/AHTgoq57d3mG4y22
jXqa8iZmHm4y1tm60/b44gdHbVEdRko1ntCoaOTKqMOJhIWMMO6EjfawcpNwb3Pu
v5PWDGyqWsPEA9ZJ74WZNVAcZkY0ei9jF/lyzJGiNjEKgduQshac4EjZRchFDCZi
5fYmWVyKXn8DZAWxA03ng6pFI/OajcSvSVGa9tTsLb0H1wVvZqX4x7k3u2F3uoe1
Nd0CAwEAAaNgMF4wHwYDVR0jBBgwFoAUqHWRrqPVtVv1vCaOqsZ0Z00nZ24wHQYD
VR0OBBYEFLgIoaMa8XrekF2uZJEgiGTR5NDJMAwGA1UdEwEB/wQCMAAwDgYDVR0P
AQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCg7mMDsgkI33zT4pBQwC8Yyyxs
uwEuMnZu0ekJdWx7UL2aEXc7zyFNDPYEilE3CF7UyQ3D0yn43vIN+jKNSHVtnVWK
Np1B6aT0x2Cd1PDsi0OOoFuhxkQ1evLbfrEh9ECJOYRd3ll2v0yPOpTDAN/nHEOy
tbbRvbt4lwYQagahz+tJcyv0LsZj5rY/m5bDKRTcWj0IdGKF2Y6fc616AvJf/G3s
kHYSGXsOzJJ4gwENzYiGmIS9m6RmSjrwu/zy2dsJ3VHgqmKgeVOaQEkr1Zz4L47m
MlGKkBLCUcmTfc8v2fUf6YVPXwgLjVpzvZ2OU1u5AVMUPat7r16d2Xcy0O76
-----END CERTIFICATE-----
)EOF";

static const char aws_private_key[] PROGMEM = R"EOF(
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)EOF";

void initializeMQTT() {
  DEBUG_PRINTLN("Initializing MQTT manager...");
  
  // Set buffer sizes
  provisioningMqtt.setBufferSize(MQTT_BUFFER_SIZE);
  awsMqtt.setBufferSize(MQTT_BUFFER_SIZE);
  
  // Set callbacks
  provisioningMqtt.setCallback(mqttMessageCallback);
  awsMqtt.setCallback(mqttMessageCallback);
  
  DEBUG_PRINTLN("MQTT manager initialized");
}

void setupMQTTForCurrentStatus() {
  DEBUG_PRINTF("Setting up MQTT for status: %d\n", config.status_code);
  
  if (isOperationalStatus() || config.status_code == STATUS_TEENAGER) {
    // Use AWS IoT for operational and verification modes
    DEBUG_PRINTLN("Configuring AWS IoT MQTT...");
    
    // Load certificates
    if (strlen(config.mqtt_cert) > 0 && strlen(config.mqtt_key) > 0 && strlen(config.mqtt_ca) > 0) {
      // Use certificates from configuration
      awsWifiClient.setCACert(config.mqtt_ca);
      awsWifiClient.setCertificate(config.mqtt_cert);
      awsWifiClient.setPrivateKey(config.mqtt_key);
      DEBUG_PRINTLN("Using certificates from configuration");
    } else {
      // Use embedded certificates
      awsWifiClient.setCACert(aws_ca_cert);
      awsWifiClient.setCertificate(aws_device_cert);
      awsWifiClient.setPrivateKey(aws_private_key);
      DEBUG_PRINTLN("Using embedded certificates");
    }
    
    awsMqtt.setServer(config.mqtt_endpoint, config.mqtt_port);
    currentMqtt = &awsMqtt;
    isUsingAWS = true;
    
  } else {
    // Use provisioning MQTT for newborn/toddler/child/geezer
    DEBUG_PRINTLN("Configuring provisioning MQTT...");
    
    provisioningMqtt.setServer(config.mqtt_endpoint, config.mqtt_port);
    currentMqtt = &provisioningMqtt;
    isUsingAWS = false;
  }
  
  DEBUG_PRINTF("MQTT setup complete. Using %s\n", isUsingAWS ? "AWS IoT" : "Provisioning");
}

bool connectMQTT() {
  if (!currentMqtt) {
    DEBUG_PRINTLN("No MQTT client configured");
    return false;
  }
  
  if (currentMqtt->connected()) {
    return true; // Already connected
  }
  
  DEBUG_PRINTF("Attempting MQTT connection to %s:%d\n", 
               config.mqtt_endpoint, config.mqtt_port);
  DEBUG_PRINTF("Client ID: %s\n", config.mqtt_client_id);
  
  bool connected = false;
  lastConnectionAttempt = millis();
  
  if (isUsingAWS) {
    // AWS IoT connection (certificate-based)
    connected = currentMqtt->connect(config.mqtt_client_id);
  } else {
    // Provisioning connection (username/password)
    if (strlen(config.mqtt_username) > 0) {
      connected = currentMqtt->connect(config.mqtt_client_id, 
                                     config.mqtt_username, 
                                     config.mqtt_password);
    } else {
      connected = currentMqtt->connect(config.mqtt_client_id);
    }
  }
  
  if (connected) {
    mqttConnected = true;
    DEBUG_PRINTLN("MQTT connected successfully!");
    
    // Subscribe to appropriate topics
    String configTopic, commandTopic;

    if (isOperationalStatus() || config.status_code == STATUS_TEENAGER) {
      // Operational topics
      configTopic = String(POC_CONFIG_PREFIX) + deviceId + CONFIG_SUFFIX;
      commandTopic = String(POC_COMMAND_PREFIX) + deviceId + COMMAND_SUFFIX;
    } else {
      // Provisioning topics
      configTopic = String(CONFIG_TOPIC_PREFIX) + deviceId;
      commandTopic = String(COMMAND_TOPIC_PREFIX) + deviceId;
    }

    currentMqtt->subscribe(configTopic.c_str());
    currentMqtt->subscribe(commandTopic.c_str());

    DEBUG_PRINTF("Subscribed to config: %s\n", configTopic.c_str());
    DEBUG_PRINTF("Subscribed to command: %s\n", commandTopic.c_str());
    
    // Handle successful connection
    handleSuccessfulConnection();
    
    return true;
  } else {
    mqttConnected = false;
    DEBUG_PRINTF("MQTT connection failed. State: %d\n", currentMqtt->state());
    
    // Handle connection failure
    handleConnectionFailure();
    
    return false;
  }
}

void subscribeToTopics() {
  if (!currentMqtt || !currentMqtt->connected()) {
    return;
  }
  
  String configTopic, commandTopic;
  
  if (isOperationalStatus() || config.status_code == STATUS_TEENAGER) {
    // Operational topics
    configTopic = String(POC_CONFIG_PREFIX) + deviceId + CONFIG_SUFFIX;
    commandTopic = String(POC_COMMAND_PREFIX) + deviceId + COMMAND_SUFFIX;
  } else {
    // Provisioning topics
    configTopic = String(CONFIG_TOPIC_PREFIX) + deviceId;
    commandTopic = String(COMMAND_TOPIC_PREFIX) + deviceId;
  }
  
  currentMqtt->subscribe(configTopic.c_str());
  currentMqtt->subscribe(commandTopic.c_str());
  
  DEBUG_PRINTF("Subscribed to config: %s\n", configTopic.c_str());
  DEBUG_PRINTF("Subscribed to command: %s\n", commandTopic.c_str());
}

void disconnectMQTT() {
  if (currentMqtt && currentMqtt->connected()) {
    currentMqtt->disconnect();
    DEBUG_PRINTLN("MQTT disconnected");
  }
  mqttConnected = false;
}

bool isMQTTConnected() {
  return mqttConnected && currentMqtt && currentMqtt->connected();
}

void loopMQTT() {
  if (currentMqtt && currentMqtt->connected()) {
    currentMqtt->loop();
  } else {
    mqttConnected = false;
  }
}

bool publishMessage(const char* topic, const char* payload, bool retain) {
  if (!currentMqtt || !currentMqtt->connected()) {
    DEBUG_PRINTLN("Cannot publish: MQTT not connected");
    return false;
  }
  
  bool result = currentMqtt->publish(topic, payload, retain);
  
  if (result) {
    DEBUG_PRINTF("Published to %s: %s\n", topic, payload);
  } else {
    DEBUG_PRINTF("Failed to publish to %s\n", topic);
  }
  
  return result;
}

String getConfigTopic() {
  if (isOperationalStatus() || config.status_code == STATUS_TEENAGER) {
    return String(POC_CONFIG_PREFIX) + deviceId + CONFIG_SUFFIX;
  } else {
    return String(CONFIG_TOPIC_PREFIX) + deviceId;
  }
}

String getCommandTopic() {
  if (isOperationalStatus() || config.status_code == STATUS_TEENAGER) {
    return String(POC_COMMAND_PREFIX) + deviceId + COMMAND_SUFFIX;
  } else {
    return String(COMMAND_TOPIC_PREFIX) + deviceId;
  }
}

String getTelemetryTopic() {
  if (config.status_code == STATUS_POC) {
    return String(POC_TELEMETRY_PREFIX) + deviceId + TELEMETRY_SUFFIX;
  } else if (isOperationalStatus()) {
    return String(POC_TELEMETRY_PREFIX) + TELEMETRY_SUFFIX;
  } else {
    return String(HELLO_TOPIC_PREFIX) + deviceId; // Use hello topic for provisioning
  }
}

String getCommandAckTopic() {
  if (isOperationalStatus() || config.status_code == STATUS_TEENAGER) {
    return String(POC_COMMAND_PREFIX) + deviceId + "/ack";
  } else {
    return String(COMMAND_ACK_TOPIC_PREFIX) + deviceId;
  }
}

bool isUsingAWSMQTT() {
  return isUsingAWS;
}

void printMQTTStatus() {
  DEBUG_PRINTLN("\n===== MQTT STATUS =====");
  DEBUG_PRINTF("Connected: %s\n", isMQTTConnected() ? "Yes" : "No");
  DEBUG_PRINTF("Using: %s\n", isUsingAWS ? "AWS IoT" : "Provisioning");
  DEBUG_PRINTF("Endpoint: %s:%d\n", config.mqtt_endpoint, config.mqtt_port);
  DEBUG_PRINTF("Client ID: %s\n", config.mqtt_client_id);
  if (currentMqtt) {
    DEBUG_PRINTF("State: %d\n", currentMqtt->state());
  }
  DEBUG_PRINTF("Config topic: %s\n", getConfigTopic().c_str());
  DEBUG_PRINTF("Command topic: %s\n", getCommandTopic().c_str());
  DEBUG_PRINTF("Telemetry topic: %s\n", getTelemetryTopic().c_str());
  DEBUG_PRINTLN("=======================\n");
}
