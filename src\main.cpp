/*
 * ESP32 Multi-Board Sensor Firmware with Provisioning and Lifecycle Management
 * 
 * Features:
 * - Device lifecycle management (newborn -> toddler -> child -> teenager -> adult -> geezer)
 * - Automatic provisioning via MQTT
 * - Base62 device ID generation from MAC address
 * - Configuration management with EEPROM storage
 * - Command processing and acknowledgment
 * - Multiple initial operating modes (POC, newborn, pre-provisioned)
 * 
 * Author: Generated for ESP32 sensor project with provisioning
 * Version: 2.0
 */

#include <Arduino.h>
#include "config.h"

// Core ESP32 libraries
#include <WiFi.h>
#include <WiFiClient.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <Wire.h>
#include <EEPROM.h>

// Time libraries
#include <WiFiUdp.h>
#include <NTPClient.h>
#include <time.h>

// Sensor libraries
#include <Adafruit_AM2320.h>

// Display libraries (if available)
#if HAS_OLED
  #include <Adafruit_GFX.h>
  #include <Adafruit_SSD1306.h>
  #include "icons.h"
#endif

// ===== GLOBAL VARIABLES =====

// Device configuration (loaded from EEPROM)
device_config_t config;

// Device identification
String deviceId;
String macAddress;

// Network objects
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, NTP_SERVER, NTP_OFFSET, NTP_UPDATE_INTERVAL);
WiFiClient provisioningClient;        // For provisioning MQTT
WiFiClientSecure awsClient;           // For AWS IoT
PubSubClient provisioningMqtt(provisioningClient);
PubSubClient awsMqtt(awsClient);

// Current MQTT client pointer (switches between provisioning and AWS)
PubSubClient* currentMqtt = nullptr;

// Sensor objects
Adafruit_AM2320 am2320;

// Display object (if available)
#if HAS_OLED
  Adafruit_SSD1306 display(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);
#endif

// System state variables
bool wifiConnected = false;
bool mqttConnected = false;
bool ntpSynced = false;
bool configReceived = false;
unsigned long lastTelemetryTime = 0;
unsigned long lastSensorReadTime = 0;
unsigned long lastDisplayUpdateTime = 0;
unsigned long lastConnectionAttempt = 0;
uint8_t connectionFailures = 0;

// Sensor data structure
struct SensorData {
  float temperature = 0.0;
  float humidity = 0.0;
  float adcVoltage = 0.0;
  uint8_t binaryState = 0;
  unsigned long timestamp = 0;
  String connectionType = "none";
};

SensorData currentSensorData;

// ===== EXTERNAL FUNCTION DECLARATIONS =====
// From provisioning.cpp
void connectMQTT();
void handleConnectionFailure();
void publishHello();
void publishTelemetry();
void processConfigMessage(const String& payload);
void transitionToStatus(device_status_t newStatus);

// From commands.cpp
void processCommandMessage(const String& payload);
void sendCommandAck(const String& command, bool success, const String& message = "");
void mqttCallback(char* topic, byte* payload, unsigned int length);
bool isInSuspendMode();
String getSuspendMessage();
String getCurrentUIMessage();
void updateDisplayWithMessages();

// ===== LOCAL FUNCTION PROTOTYPES =====
void setupSystem();
void loadConfiguration();
void saveConfiguration();
void generateDeviceId();
String encodeBase62(uint64_t value, int length);
void setupWiFi();
void setupNTP();
void setupMQTT();
void setupSensors();
void setupDisplay();
void readSensors();
void updateDisplay();
uint32_t calculateCRC(const device_config_t* cfg);
void resetConfiguration();

// ===== MAIN FUNCTIONS =====

void setup() {
  Serial.begin(SERIAL_BAUD);
  delay(1000);
  
  DEBUG_PRINTLN("\n===== ESP32 Sensor with Provisioning =====");
  DEBUG_PRINTF("Firmware: %s\n", FIRMWARE_VERSION);
  DEBUG_PRINTF("Hardware: %s\n", HARDWARE_VERSION);
  DEBUG_PRINTF("Free heap at start: %d bytes\n", ESP.getFreeHeap());
  
  setupSystem();
  loadConfiguration();
  setupSensors();
  setupDisplay();
  setupWiFi();
  
  if (wifiConnected) {
    setupNTP();
    setupMQTT();
  }
  
  DEBUG_PRINTF("Device ID: %s\n", deviceId.c_str());
  DEBUG_PRINTF("Status: %d\n", config.status_code);
  DEBUG_PRINTLN("Setup complete. Starting main loop...");
}

void loop() {
  unsigned long now = millis();
  
  // Read sensors periodically
  if (now - lastSensorReadTime > config.sensor_read_interval_ms) {
    readSensors();
    lastSensorReadTime = now;
  }
  
  // Update display periodically
  #if HAS_OLED
  if (now - lastDisplayUpdateTime > config.display_refresh_interval_ms) {
    updateDisplay();
    lastDisplayUpdateTime = now;
  }
  #endif
  
  // Handle MQTT connection and messaging
  if (currentMqtt && currentMqtt->connected()) {
    currentMqtt->loop();
    mqttConnected = true;
  } else {
    mqttConnected = false;
    
    // Try to reconnect if enough time has passed
    if (now - lastConnectionAttempt > config.connection_retry_interval_ms) {
      connectMQTT();
      lastConnectionAttempt = now;
    }
  }
  
  // Publish telemetry or hello based on status
  if (mqttConnected) {
    if (config.status_code == STATUS_ADULT || config.status_code == STATUS_POC) {
      // Adult/POC mode: publish telemetry
      if (now - lastTelemetryTime > (config.publish_interval_sec * 1000)) {
        publishTelemetry();
        lastTelemetryTime = now;
      }
    } else if (config.status_code == STATUS_NEWBORN || config.status_code == STATUS_TODDLER) {
      // Newborn/Toddler mode: publish hello and wait for config
      if (now - lastTelemetryTime > 30000) {  // Every 30 seconds
        publishHello();
        lastTelemetryTime = now;
      }
    }
  }
  
  delay(100); // Small delay to prevent tight loop
}

// ===== IMPLEMENTATION FUNCTIONS =====

void setupSystem() {
  DEBUG_PRINTLN("Initializing system...");

  // Initialize built-in LED
  pinMode(STATUS_LED_PIN, OUTPUT);
  digitalWrite(STATUS_LED_PIN, LOW);

  // Initialize digital inputs with pullups
  pinMode(DIN1_PIN, INPUT_PULLUP);
  pinMode(DIN2_PIN, INPUT_PULLUP);

  // Initialize I2C
  Wire.begin(I2C_SDA, I2C_SCL);

  // Initialize EEPROM
  EEPROM.begin(EEPROM_SIZE);

  // Generate unique device ID from MAC address
  generateDeviceId();

  DEBUG_PRINTLN("System initialization complete.");
}

void generateDeviceId() {
  // Get MAC address
  uint8_t mac[6];
  WiFi.macAddress(mac);

  // Store MAC as string for reference
  macAddress = WiFi.macAddress();

  // Convert MAC to 48-bit integer
  uint64_t macInt = 0;
  for (int i = 0; i < 6; i++) {
    macInt = (macInt << 8) | mac[i];
  }

  // Encode as 6-character base62 string
  deviceId = encodeBase62(macInt, 6);

  DEBUG_PRINTF("MAC Address: %s\n", macAddress.c_str());
  DEBUG_PRINTF("Device ID: %s\n", deviceId.c_str());
}

String encodeBase62(uint64_t value, int length) {
  String result = "";

  if (value == 0) {
    result = "0";
  } else {
    while (value > 0) {
      result = base62_chars[value % 62] + result;
      value /= 62;
    }
  }

  // Pad with leading zeros if needed
  while (result.length() < length) {
    result = "0" + result;
  }

  return result.substring(0, length);
}

void loadConfiguration() {
  DEBUG_PRINTLN("Loading configuration from EEPROM...");

  // Read configuration from EEPROM
  EEPROM.get(0, config);

  // Validate configuration
  uint32_t storedCRC = config.config_crc;
  config.config_crc = 0;  // Clear CRC for calculation
  uint32_t calculatedCRC = calculateCRC(&config);

  if (config.config_version != CONFIG_VERSION || storedCRC != calculatedCRC) {
    DEBUG_PRINTLN("Invalid or corrupted configuration, using defaults...");
    resetConfiguration();
  } else {
    DEBUG_PRINTLN("Valid configuration loaded from EEPROM.");
    config.config_crc = storedCRC;  // Restore CRC
  }

  // Copy device ID to config
  strncpy(config.device_id, deviceId.c_str(), sizeof(config.device_id) - 1);
  config.device_id[sizeof(config.device_id) - 1] = '\0';
}

void resetConfiguration() {
  DEBUG_PRINTLN("Resetting configuration to defaults...");

  // Clear entire config structure
  memset(&config, 0, sizeof(config));

  // Set version
  config.config_version = CONFIG_VERSION;

  // Set initial status based on compile-time mode
  #ifdef INITIAL_MODE_POC
    config.status_code = STATUS_POC;
  #elif defined(INITIAL_MODE_PREPROV)
    config.status_code = STATUS_TEENAGER;  // Pre-provisioned, needs verification
  #else
    config.status_code = STATUS_NEWBORN;   // Default to newborn
  #endif

  // Device identification
  strncpy(config.device_id, deviceId.c_str(), sizeof(config.device_id) - 1);
  strncpy(config.device_type, DEVICE_TYPE, sizeof(config.device_type) - 1);
  strncpy(config.firmware_version, FIRMWARE_VERSION, sizeof(config.firmware_version) - 1);
  strncpy(config.hardware_version, HARDWARE_VERSION, sizeof(config.hardware_version) - 1);
  strncpy(config.ui_name, DEFAULT_SENSOR_NAME, sizeof(config.ui_name) - 1);

  // Network defaults
  strncpy(config.wifi_ssid, DEFAULT_WIFI_SSID, sizeof(config.wifi_ssid) - 1);
  strncpy(config.wifi_pass, DEFAULT_WIFI_PASS, sizeof(config.wifi_pass) - 1);
  config.use_static_ip = false;

  // MQTT defaults
  if (config.status_code == STATUS_POC) {
    strncpy(config.mqtt_endpoint, AWS_IOT_ENDPOINT, sizeof(config.mqtt_endpoint) - 1);
    config.mqtt_port = AWS_IOT_PORT;
    strncpy(config.mqtt_client_id, AWS_THING_NAME, sizeof(config.mqtt_client_id) - 1);
  } else {
    strncpy(config.mqtt_endpoint, PROVISIONING_ENDPOINT, sizeof(config.mqtt_endpoint) - 1);
    config.mqtt_port = PROVISIONING_PORT;
    strncpy(config.mqtt_client_id, deviceId.c_str(), sizeof(config.mqtt_client_id) - 1);
    strncpy(config.mqtt_username, PROVISIONING_USERNAME, sizeof(config.mqtt_username) - 1);
    strncpy(config.mqtt_password, PROVISIONING_PASSWORD, sizeof(config.mqtt_password) - 1);
  }

  // Publishing defaults
  config.publish_interval_sec = DEFAULT_PUBLISH_INTERVAL;
  config.publish_regardless_of_change = false;
  config.stay_connected = true;

  // Sensor calibration defaults
  config.adc_offset = DEFAULT_ADC_OFFSET;
  config.adc_multiplier = DEFAULT_ADC_MULTIPLIER;
  config.adc_offset_first = true;
  config.adc_decimal_places = DEFAULT_DECIMAL_PLACES;
  strncpy(config.adc_label, DEFAULT_ADC_LABEL, sizeof(config.adc_label) - 1);

  config.temp_offset = DEFAULT_TEMP_OFFSET;
  config.temp_multiplier = 1.0f;
  config.temp_offset_first = true;
  config.temp_decimal_places = DEFAULT_DECIMAL_PLACES;
  config.temp_unit = DEFAULT_TEMP_UNIT;

  config.hum_offset = DEFAULT_HUM_OFFSET;
  config.hum_multiplier = 1.0f;
  config.hum_offset_first = true;
  config.hum_decimal_places = DEFAULT_DECIMAL_PLACES;

  // Binary sensor defaults
  config.bin1_invert = false;
  strncpy(config.bin1_label_on, DEFAULT_BIN1_LABEL_ON, sizeof(config.bin1_label_on) - 1);
  strncpy(config.bin1_label_off, DEFAULT_BIN1_LABEL_OFF, sizeof(config.bin1_label_off) - 1);

  config.bin2_invert = false;
  strncpy(config.bin2_label_on, DEFAULT_BIN2_LABEL_ON, sizeof(config.bin2_label_on) - 1);
  strncpy(config.bin2_label_off, DEFAULT_BIN2_LABEL_OFF, sizeof(config.bin2_label_off) - 1);

  // Display defaults
  config.sensor_read_interval_ms = DEFAULT_SENSOR_READ_INTERVAL;
  config.display_refresh_interval_ms = DEFAULT_DISPLAY_REFRESH_INTERVAL;

  // Connection defaults
  config.connection_retry_interval_ms = PROVISIONING_RETRY_INTERVAL;
  config.max_connection_failures = DEFAULT_MAX_FAILURES;

  // Save to EEPROM
  saveConfiguration();
}

void saveConfiguration() {
  DEBUG_PRINTLN("Saving configuration to EEPROM...");

  // Calculate and store CRC
  config.config_crc = 0;  // Clear CRC for calculation
  config.config_crc = calculateCRC(&config);

  // Write to EEPROM
  EEPROM.put(0, config);
  EEPROM.commit();

  DEBUG_PRINTLN("Configuration saved to EEPROM.");
}

uint32_t calculateCRC(const device_config_t* cfg) {
  // Simple CRC32 calculation
  uint32_t crc = 0xFFFFFFFF;
  const uint8_t* data = (const uint8_t*)cfg;

  for (size_t i = 0; i < sizeof(device_config_t); i++) {
    crc ^= data[i];
    for (int j = 0; j < 8; j++) {
      if (crc & 1) {
        crc = (crc >> 1) ^ 0xEDB88320;
      } else {
        crc >>= 1;
      }
    }
  }

  return ~crc;
}

void setupWiFi() {
  DEBUG_PRINTF("Connecting to WiFi: %s\n", config.wifi_ssid);

  WiFi.mode(WIFI_STA);

  if (config.use_static_ip) {
    IPAddress ip(config.static_ip);
    IPAddress gateway(config.static_gateway);
    IPAddress subnet(config.static_subnet);
    IPAddress dns1(config.static_dns1);
    IPAddress dns2(config.static_dns2);

    if (!WiFi.config(ip, gateway, subnet, dns1, dns2)) {
      DEBUG_PRINTLN("Static IP configuration failed, falling back to DHCP");
      config.use_static_ip = false;
    }
  }

  WiFi.begin(config.wifi_ssid, config.wifi_pass);

  unsigned long startTime = millis();
  while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < WIFI_CONNECT_TIMEOUT) {
    delay(500);
    DEBUG_PRINT(".");
  }

  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    DEBUG_PRINTLN("\nWiFi connected!");
    DEBUG_PRINTF("IP: %s\n", WiFi.localIP().toString().c_str());
    DEBUG_PRINTF("RSSI: %d dBm\n", WiFi.RSSI());
    digitalWrite(STATUS_LED_PIN, HIGH);
  } else {
    wifiConnected = false;
    DEBUG_PRINTLN("\nWiFi connection failed!");
    digitalWrite(STATUS_LED_PIN, LOW);
  }
}

void setupNTP() {
  if (!wifiConnected) return;

  DEBUG_PRINTLN("Starting NTP client...");
  timeClient.begin();

  int attempts = 0;
  while (!timeClient.update() && attempts < 10) {
    delay(1000);
    attempts++;
    DEBUG_PRINT(".");
  }

  if (timeClient.isTimeSet()) {
    ntpSynced = true;
    time_t epochTime = timeClient.getEpochTime();
    DEBUG_PRINTLN("\nNTP time synchronized!");
    DEBUG_PRINTF("Current time: %s\n", timeClient.getFormattedTime().c_str());
  } else {
    ntpSynced = false;
    DEBUG_PRINTLN("\nNTP sync failed!");
  }
}

void setupMQTT() {
  if (!wifiConnected) return;

  DEBUG_PRINTLN("Setting up MQTT...");

  // Choose MQTT client based on status
  if (config.status_code == STATUS_ADULT || config.status_code == STATUS_POC) {
    // Use AWS IoT client
    currentMqtt = &awsMqtt;

    // Load certificates for AWS IoT (if available)
    if (strlen(config.mqtt_cert) > 0 && strlen(config.mqtt_key) > 0 && strlen(config.mqtt_ca) > 0) {
      awsClient.setCACert(config.mqtt_ca);
      awsClient.setCertificate(config.mqtt_cert);
      awsClient.setPrivateKey(config.mqtt_key);
    }

    awsMqtt.setServer(config.mqtt_endpoint, config.mqtt_port);
    awsMqtt.setCallback(mqttCallback);
    awsMqtt.setBufferSize(MQTT_BUFFER_SIZE);

  } else {
    // Use provisioning client
    currentMqtt = &provisioningMqtt;

    provisioningMqtt.setServer(config.mqtt_endpoint, config.mqtt_port);
    provisioningMqtt.setCallback(mqttCallback);
    provisioningMqtt.setBufferSize(MQTT_BUFFER_SIZE);
  }

  DEBUG_PRINTF("MQTT setup complete for %s mode.\n",
               (currentMqtt == &awsMqtt) ? "AWS" : "provisioning");
}

void setupSensors() {
  DEBUG_PRINTLN("Initializing sensors...");

  // Initialize AM2320 sensor
  if (am2320.begin()) {
    DEBUG_PRINTLN("AM2320 sensor initialized successfully.");
  } else {
    DEBUG_PRINTLN("Failed to initialize AM2320 sensor!");
  }

  DEBUG_PRINTLN("Sensor initialization complete.");
}

void setupDisplay() {
  #if HAS_OLED
  DEBUG_PRINTLN("Initializing OLED display...");

  if (display.begin(SSD1306_SWITCHCAPVCC, OLED_ADDRESS)) {
    DEBUG_PRINTLN("OLED display initialized successfully.");

    // Clear display and show startup message
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("ESP32 Sensor v2.0");
    display.println(BOARD_NAME);
    display.printf("ID: %s", deviceId.substring(0, 6).c_str());
    display.setCursor(0, 30);
    display.printf("Status: %d", config.status_code);
    display.display();

  } else {
    DEBUG_PRINTLN("Failed to initialize OLED display!");
  }
  #else
  DEBUG_PRINTLN("No OLED display available on this board.");
  #endif
}

void readSensors() {
  // Read AM2320 temperature and humidity
  float temp = am2320.readTemperature();
  float hum = am2320.readHumidity();

  if (!isnan(temp) && !isnan(hum)) {
    // Apply calibration
    if (config.temp_offset_first) {
      currentSensorData.temperature = (temp + config.temp_offset) * config.temp_multiplier;
    } else {
      currentSensorData.temperature = (temp * config.temp_multiplier) + config.temp_offset;
    }

    if (config.hum_offset_first) {
      currentSensorData.humidity = (hum + config.hum_offset) * config.hum_multiplier;
    } else {
      currentSensorData.humidity = (hum * config.hum_multiplier) + config.hum_offset;
    }

    // Convert temperature unit if needed
    if (config.temp_unit == 'F') {
      currentSensorData.temperature = (currentSensorData.temperature * 9.0 / 5.0) + 32.0;
    }
  }

  // Read ADC voltage
  int adcRaw = analogRead(ADC_PIN);
  float adcVoltage = (adcRaw / 4095.0) * 3.3; // ESP32 ADC reference is 3.3V

  // Apply ADC calibration
  if (config.adc_offset_first) {
    currentSensorData.adcVoltage = (adcVoltage + config.adc_offset) * config.adc_multiplier;
  } else {
    currentSensorData.adcVoltage = (adcVoltage * config.adc_multiplier) + config.adc_offset;
  }

  // Read digital inputs
  bool din1 = digitalRead(DIN1_PIN);
  bool din2 = digitalRead(DIN2_PIN);

  // Apply inversion if configured
  if (config.bin1_invert) din1 = !din1;
  if (config.bin2_invert) din2 = !din2;

  // Create 2-bit binary state
  currentSensorData.binaryState = (din2 << 1) | din1;

  // Set timestamp
  currentSensorData.timestamp = timeClient.getEpochTime();

  // Set connection type
  if (currentMqtt && currentMqtt->connected()) {
    if (currentMqtt == &awsMqtt) {
      currentSensorData.connectionType = "AWS-IoT";
    } else {
      currentSensorData.connectionType = "Provisioning";
    }
  } else if (wifiConnected) {
    currentSensorData.connectionType = "WiFi";
  } else {
    currentSensorData.connectionType = "none";
  }
}

void updateDisplay() {
  #if HAS_OLED
  // Check for command messages first
  updateDisplayWithMessages();

  // If no command messages, show normal display
  String currentMsg = getCurrentUIMessage();
  if (currentMsg.length() == 0 && !isInSuspendMode()) {
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);

    // Line 1: Device name or type
    display.setCursor(0, 0);
    if (strlen(config.ui_name) > 0) {
      display.printf("%s", config.ui_name);
    } else {
      display.printf("%s %s", config.device_type, deviceId.substring(0, 4).c_str());
    }

    // Line 2: Temperature
    display.setCursor(0, 10);
    display.printf("Temp: %.*f %c", config.temp_decimal_places,
                   currentSensorData.temperature, config.temp_unit);

    // Line 3: Humidity
    display.setCursor(0, 20);
    display.printf("Hum:  %.*f %%", config.hum_decimal_places,
                   currentSensorData.humidity);

    // Line 4: ADC
    display.setCursor(0, 30);
    display.printf("%s: %.*f V", config.adc_label, config.adc_decimal_places,
                   currentSensorData.adcVoltage);

    // Line 5: Digital inputs
    display.setCursor(0, 40);
    const char* bin1_label = (currentSensorData.binaryState & 1) ?
                             config.bin1_label_on : config.bin1_label_off;
    const char* bin2_label = (currentSensorData.binaryState & 2) ?
                             config.bin2_label_on : config.bin2_label_off;
    display.printf("%s %s", bin1_label, bin2_label);

    // Line 6: Status
    display.setCursor(0, 50);
    if (mqttConnected) {
      display.print("MQTT: OK");
      display.drawBitmap(110, 50, wifi_icon_6, 8, 8, SSD1306_WHITE);
      display.drawBitmap(120, 50, checkmark_icon, 8, 8, SSD1306_WHITE);
    } else if (wifiConnected) {
      display.print("WiFi: OK");
      display.drawBitmap(110, 50, wifi_icon_4, 8, 8, SSD1306_WHITE);
      display.drawBitmap(120, 50, x_icon, 8, 8, SSD1306_WHITE);
    } else {
      display.print("No Connection");
      display.drawBitmap(110, 50, wifi_icon_0, 8, 8, SSD1306_WHITE);
      display.drawBitmap(120, 50, x_icon, 8, 8, SSD1306_WHITE);
    }

    display.display();
  }
  #endif
}
