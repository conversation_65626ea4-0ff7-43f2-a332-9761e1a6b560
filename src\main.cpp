// ESP8266 AWS IoT Core Telemetry Publisher v1.0
// - AWS IoT Core MQTT over TLS/SSL connectivity
// - AM2320 temperature/humidity sensor
// - ADC voltage reading (0-3.3V)
// - Digital binary inputs on D5/D6
// - SSD1306 OLED display with status icons
// - NTP time synchronization for ISO 8601 timestamps
// - WiFi captive portal configuration

// === LIBRARIES ===
#include <ESP8266WiFi.h>
#include <ESP8266WebServer.h>
#include <DNSServer.h>
#include <WiFiClientSecure.h>     // For TLS/SSL connection to AWS IoT
#include <PubSubClient.h>         // MQTT client for AWS IoT
#include <Wire.h>
#include <Adafruit_Sensor.h>
#include <Adafruit_AM2320.h>       // AM2320 Temp/Humidity Sensor
#include <Adafruit_SSD1306.h>     // OLED Display Driver
#include <Adafruit_GFX.h>         // Graphics library for display
#include <ArduinoJson.h>          // JSON Payload Handling
#include <EEPROM.h>               // Configuration Storage
#include <WiFiUdp.h>              // For NTP
#include <NTPClient.h>            // NTP time synchronization
#include <TimeLib.h>              // Time manipulation
#include <icons.h>                // 8byte display icons for wifi and mqtt status

// === FIRMWARE & DEVICE INFO ===
const char* FIRMWARE_VERSION = "v1.0";
const int DEVICE_TYPE_ID = 101; // Unique identifier for this *type* of device

// === PIN CONFIGURATION ===
const int SDA_PIN = D2; // I2C Data for Sensor/Display
const int SCL_PIN = D1; // I2C Clock for Sensor/Display
const int BINARY_INPUT_D5 = D5; // GPIO14 - Binary input LSB
const int BINARY_INPUT_D6 = D6; // GPIO12 - Binary input MSB
// Note: A0 is used implicitly by analogRead(A0)

// === EEPROM CONFIGURATION ===
// Using EEPROM to store persistent configuration data
#define EEPROM_SIZE 512 // Increased size slightly for safety, adjust if needed

// EEPROM Addresses (ensure no overlaps)
#define EEPROM_ADDR_ADC_CAL          0  // Float (4 bytes)
#define EEPROM_ADDR_TEMP_CAL         4  // Float (4 bytes)
#define EEPROM_ADDR_HUMID_CAL        8  // Float (4 bytes)
#define EEPROM_ADDR_NAME             12 // String (up to FRIENDLY_NAME_MAXLEN bytes)
#define FRIENDLY_NAME_MAXLEN         48 // Max length for friendly name (incl. null)
#define EEPROM_ADDR_PUBLISH_INTERVAL (EEPROM_ADDR_NAME + FRIENDLY_NAME_MAXLEN) // Unsigned Long (4 bytes) ~60
// --- Gap ---
#define EEPROM_ADDR_WIFI_SSID        128 // String (up to WIFI_CRED_MAXLEN bytes)
#define WIFI_CRED_MAXLEN             64  // Max length for SSID/Password (incl. null)
#define EEPROM_ADDR_WIFI_PASS        (EEPROM_ADDR_WIFI_SSID + WIFI_CRED_MAXLEN) // String (up to WIFI_CRED_MAXLEN bytes) ~192

// === WIFI CONFIGURATION ===
#define WIFI_RETRY_LIMIT 30        // How many 500ms attempts before AP mode
#define WIFI_CONNECT_TIMEOUT_MS (WIFI_RETRY_LIMIT * 500)
#define WIFI_SSID "HaciendaHagansLR"
#define WIFI_PASS "F1nglongers"
char wifi_ssid[WIFI_CRED_MAXLEN];                 // Buffer for current SSID
char wifi_pass[WIFI_CRED_MAXLEN];                 // Buffer for current Password

// === AWS IOT CORE CONFIGURATION ===
const char* aws_endpoint = "a2ltfva6jq44zv-ats.iot.us-east-1.amazonaws.com";
const int aws_port = 8883;

// === AWS IOT CORE CERTIFICATES ===
// === AWS IOT CORE CERTIFICATES (stored in flash using PROGMEM) ===
static const char certificate_pem_crt[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDWTCCAkGgAwIBAgIUQLLrIO5d6G9m6/tgpMttQs9ncW0wDQYJKoZIhvcNAQEL
BQAwTTFLMEkGA1UECwxCQW1hem9uIFdlYiBTZXJ2aWNlcyBPPUFtYXpvbi5jb20g
SW5jLiBMPVNlYXR0bGUgU1Q9V2FzaGluZ3RvbiBDPVVTMB4XDTI1MDYxMzAwNTgx
NVoXDTQ5MTIzMTIzNTk1OVowHjEcMBoGA1UEAwwTQVdTIElvVCBDZXJ0aWZpY2F0
ZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKsW39KzechA5NkWWfAF
5H6sNeICnc9hjGjZQ3tI6VSHjDFhGNtnf+2eC6HQZ/m86jSOtmJnvYD0lBd/4cle
ZT/LLiDTRgyFL+GKnmgRN0KhNsvgVaR1tnTMY+AszAhpgBf/AHTgoq57d3mG4y22
jXqa8iZmHm4y1tm60/b44gdHbVEdRko1ntCoaOTKqMOJhIWMMO6EjfawcpNwb3Pu
v5PWDGyqWsPEA9ZJ74WZNVAcZkY0ei9jF/lyzJGiNjEKgduQshac4EjZRchFDCZi
5fYmWVyKXn8DZAWxA03ng6pFI/OajcSvSVGa9tTsLb0H1wVvZqX4x7k3u2F3uoe1
Nd0CAwEAAaNgMF4wHwYDVR0jBBgwFoAUqHWRrqPVtVv1vCaOqsZ0Z00nZ24wHQYD
VR0OBBYEFLgIoaMa8XrekF2uZJEgiGTR5NDJMAwGA1UdEwEB/wQCMAAwDgYDVR0P
AQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCg7mMDsgkI33zT4pBQwC8Yyyxs
uwEuMnZu0ekJdWx7UL2aEXc7zyFNDPYEilE3CF7UyQ3D0yn43vIN+jKNSHVtnVWK
Np1B6aT0x2Cd1PDsi0OOoFuhxkQ1evLbfrEh9ECJOYRd3ll2v0yPOpTDAN/nHEOy
tbbRvbt4lwYQagahz+tJcyv0LsZj5rY/m5bDKRTcWj0IdGKF2Y6fc616AvJf/G3s
kHYSGXsOzJJ4gwENzYiGmIS9m6RmSjrwu/zy2dsJ3VHgqmKgeVOaQEkr1Zz4L47m
MlGKkBLCUcmTfc8v2fUf6YVPXwgLjVpzvZ2OU1u5AVMUPat7r16d2Xcy0O76
-----END CERTIFICATE-----
)EOF";

static const char private_pem_key[] PROGMEM = R"EOF(
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)EOF";

static const char amazon_ca_cert[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
ADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6
b24gUm9vdCBDQSAxMB4XDTE1MDUyNjAwMDAwMFoXDTM4MDExNzAwMDAwMFowOTEL
MAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv
b3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj
ca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM
9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw
IFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6
VOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L
93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm
jgSubJrIqg0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0OBBYEFIQYzIU07LwMlJQuCFmcx7IQTgoIMA0GCSqGSIb3DQEBCwUA
A4IBAQCY8jdaQZChGsV2USggNiMOruYou6r4lK5IpDB/G/wkjUu0yKGX9rbxenDI
U5PMCCjjmCXPI6T53iHTfIUJrU6adTrCC2qJeHZERxhlbI1Bjjt/msv0tadQ1wUs
N+gDS63pYaACbvXy8MWy7Vu33PqUXHeeE6V/Uq2V8viTO96LXFvKWlJbYK8U90vv
o/ufQJVtMVT8QtPHRh8jrdkPSHCa2XV4cdFyQzR1bldZwgJcJmApzyMZFo6IQ6XU
5MsI+yMRQ+hDKXJioaldXgjUkK642M4UwtBV8ob2xJNDd2ZhwLnoQdeXeGADbkpy
rqXRfboQnoZsG4q5WTP468SQvvG5
-----END CERTIFICATE-----
)EOF";


// === SENSOR CONFIGURATION ===
Adafruit_AM2320 am2320 = Adafruit_AM2320();
#define SENSOR_READ_INTERVAL_MS 2000 // How often to read the sensor (AM2320 needs >1.5s between reads)

// === DISPLAY CONFIGURATION ===
#define SCREEN_WIDTH 128 // OLED display width, in pixels
#define SCREEN_HEIGHT 32 // OLED display height, in pixels
#define OLED_RESET_PIN -1 // Reset pin # (or -1 if sharing Arduino reset pin)
#define DISPLAY_UPDATE_INTERVAL_MS 1000 // How often to refresh the display

// === TIMING & INTERVALS ===
#define PUBLISH_INTERVAL_DEFAULT_MS 60000 // Default telemetry publish interval (60 seconds)
#define AWS_RECONNECT_INTERVAL_MS 5000 // Wait 5s between AWS IoT reconnect attempts

// === NTP CONFIGURATION ===
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, "pool.ntp.org", 0, 60000); // UTC time, update every minute

// === OBJECTS ===
BearSSL::WiFiClientSecure awsClient;
PubSubClient mqttClient(awsClient);
Adafruit_SSD1306 display(SCREEN_WIDTH, SCREEN_HEIGHT, &Wire, OLED_RESET_PIN);
ESP8266WebServer webServer(80);
DNSServer dnsServer;

// === GLOBAL VARIABLES ===
bool displayOK = false;           // Was the display initialized successfully?
bool configModeActive = false;    // Is the device in AP Config Portal mode?
bool awsConnected = false;        // AWS IoT connection status
float adcCalibration = 1.00;      // Calibration factor for ADC reading
float sensorCalOffsetTemp = 0.0;  // Calibration offset for temperature
float sensorCalOffsetHumidity = 0.0;// Calibration offset for humidity
unsigned long publishIntervalMs = PUBLISH_INTERVAL_DEFAULT_MS; // Current telemetry publish interval
char friendlyName[FRIENDLY_NAME_MAXLEN] = "Type101Sensor"; // User-friendly name for the device
char nodeID[7];                   // Unique 6-char Base62 Node ID from ChipID + Null terminator

// AWS IoT Topic Strings (will be populated in setup)
String telemTopic;                // device/{uid}/telem
String configTopic;               // device/{uid}/config
String controlTopic;              // device/{uid}/control/#

// State variables for timing and last known values
unsigned long lastPublishTimestamp = 0;
unsigned long lastDisplayUpdateTimestamp = 0;
unsigned long lastSensorUpdateTimestamp = 0;
unsigned long lastAwsAttemptTimestamp = 0;
float lastTempC = NAN;            // Use NAN to indicate no valid reading yet
float lastHumidity = NAN;         // Use NAN to indicate no valid reading yet
float lastVoltage = 0.0;          // ADC voltage reading (0-3.3V)
int lastBinaryInput = 0;          // Binary input from D5/D6 (0-3)

// === FUNCTION PROTOTYPES ===
// --- Configuration (EEPROM) ---
void loadGeneralConfig();
void saveGeneralConfig();
void clearEEPROM();
void loadWiFiCredentials();
void saveWiFiCredentials(const String& newSSID, const String& newPASS);
// --- WiFi & Network ---
void setupWiFi();
void startConfigPortal();
void handleWebServer();
void handleDns();
// --- AWS IoT Core ---
void setupAWS();
void connectAWS();
void mqttCallback(const MQTT::Publish& pub);
void publishTelemetry();
void processConfigMessage(const String& payload);
// --- Time ---
void setupNTP();
String getISOTimestamp();
// --- Sensor ---
void setupSensor();
void updateSensorReadings();
void readBinaryInputs();
// --- Display ---
void setupDisplay();
void updateDisplay();
void drawTopStatusBar();
void drawPublishProgressBar();
const uint8_t* getWifiIcon(int level, uint8_t &width);
int wifiLevelFromRSSI(long rssi);
// --- Utilities ---
void generateNodeID();
void base62Encode(uint32_t value, char *output, int length);

// === UTILITIES ===
const char base62Chars[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

void base62Encode(uint32_t value, char *output, int length) {
  for (int i = length - 1; i >= 0; --i) {
    output[i] = base62Chars[value % 62];
    value /= 62;
  }
  output[length] = '\0'; // Null-terminate the string
}

void generateNodeID() {
  uint32_t chipId = ESP.getChipId();
  base62Encode(chipId, nodeID, 6); // Generate 6-character base62 ID
  Serial.print("Generated NodeID: ");
  Serial.println(nodeID);
}

// === EEPROM FUNCTIONS ===
void loadWiFiCredentials() {
  Serial.println("Loading WiFi credentials from EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  bool credsValid = true;
  // Read SSID
  for (int i = 0; i < WIFI_CRED_MAXLEN; i++) {
    wifi_ssid[i] = EEPROM.read(EEPROM_ADDR_WIFI_SSID + i);
    if (i == 0 && wifi_ssid[i] == 0xFF) { // Check if EEPROM seems uninitialized
        credsValid = false;
        break;
    }
    if (wifi_ssid[i] == '\0') break; // Stop at null terminator
  }
  wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination

  // Read Password only if SSID seemed valid
  if (credsValid) {
      for (int i = 0; i < WIFI_CRED_MAXLEN; i++) {
        wifi_pass[i] = EEPROM.read(EEPROM_ADDR_WIFI_PASS + i);
        if (wifi_pass[i] == '\0') break; // Stop at null terminator
      }
      wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
  }

  EEPROM.end();

  // If EEPROM was empty/invalid or strings are zero length, use defaults
  if (!credsValid || strlen(wifi_ssid) == 0 ) { // Don't require password to be present
    Serial.println("EEPROM WiFi credentials invalid or empty, using defaults.");
    strncpy(wifi_ssid, WIFI_SSID, WIFI_CRED_MAXLEN -1);
    strncpy(wifi_pass, WIFI_PASS, WIFI_CRED_MAXLEN -1);
    wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
    wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0';
  } else {
      Serial.println("WiFi credentials loaded successfully.");
  }

  Serial.print("SSID: "); Serial.println(wifi_ssid);
  // Avoid printing password to Serial for security, uncomment if needed for debug
  // Serial.print("Password: "); Serial.println(wifi_pass);
}

void saveWiFiCredentials(const String& newSSID, const String& newPASS) {
  Serial.println("Saving WiFi credentials to EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  // Clear old SSID area first (optional, but good practice)
  for (int i = 0; i < WIFI_CRED_MAXLEN; i++) EEPROM.write(EEPROM_ADDR_WIFI_SSID + i, 0);
  // Write new SSID
  for (unsigned int i = 0; i < newSSID.length() && i < (unsigned int)(WIFI_CRED_MAXLEN -1); i++) {
    EEPROM.write(EEPROM_ADDR_WIFI_SSID + i, newSSID[i]);
  }
  // Clear old Password area
  for (unsigned int i = 0; i < WIFI_CRED_MAXLEN; i++) EEPROM.write(EEPROM_ADDR_WIFI_PASS + i, 0);
  // Write new Password
  for (unsigned int i = 0; i < newPASS.length() && i < (unsigned int)(WIFI_CRED_MAXLEN - 1); i++) {
    EEPROM.write(EEPROM_ADDR_WIFI_PASS + i, newPASS[i]);
  }

  if (EEPROM.commit()) {
    Serial.println("EEPROM WiFi credentials saved successfully.");
  } else {
    Serial.println("ERROR: EEPROM commit failed!");
  }
  EEPROM.end();

  // Update in-memory buffers immediately
  strncpy(wifi_ssid, newSSID.c_str(), WIFI_CRED_MAXLEN -1);
  strncpy(wifi_pass, newPASS.c_str(), WIFI_CRED_MAXLEN -1);
   wifi_ssid[WIFI_CRED_MAXLEN - 1] = '\0'; // Ensure null termination
   wifi_pass[WIFI_CRED_MAXLEN - 1] = '\0';
}

void loadGeneralConfig() {
  Serial.println("Loading general config from EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  EEPROM.get(EEPROM_ADDR_ADC_CAL, adcCalibration);
  EEPROM.get(EEPROM_ADDR_TEMP_CAL, sensorCalOffsetTemp);
  EEPROM.get(EEPROM_ADDR_HUMID_CAL, sensorCalOffsetHumidity);
  EEPROM.get(EEPROM_ADDR_PUBLISH_INTERVAL, publishIntervalMs);

  // Validate loaded values and fix corruption
  if (isnan(adcCalibration) || adcCalibration < 0.1 || adcCalibration > 10.0) {
    Serial.println("Invalid ADC calibration, using default: 1.0");
    adcCalibration = 1.0;
  }
  if (isnan(sensorCalOffsetTemp) || abs(sensorCalOffsetTemp) > 50.0) {
    Serial.println("Invalid temp offset, using default: 0.0");
    sensorCalOffsetTemp = 0.0;
  }
  if (isnan(sensorCalOffsetHumidity) || abs(sensorCalOffsetHumidity) > 50.0) {
    Serial.println("Invalid humidity offset, using default: 0.0");
    sensorCalOffsetHumidity = 0.0;
  }
  if (publishIntervalMs < 5000 || publishIntervalMs > 3600000) { // 5sec to 1hr
    Serial.print("Invalid publish interval from EEPROM, using default: ");
    Serial.println(PUBLISH_INTERVAL_DEFAULT_MS);
    publishIntervalMs = PUBLISH_INTERVAL_DEFAULT_MS;
  }

  // Read friendly name string
  bool nameValid = false;
  for (int i = 0; i < FRIENDLY_NAME_MAXLEN; i++) {
    friendlyName[i] = EEPROM.read(EEPROM_ADDR_NAME + i);
    if (i==0 && friendlyName[i] != 0xFF) nameValid = true; // Basic check if initialized
    if (friendlyName[i] == '\0') break;
  }
  friendlyName[FRIENDLY_NAME_MAXLEN - 1] = '\0'; // Ensure null termination
  if (!nameValid || strlen(friendlyName) == 0) {
      Serial.println("Using default friendly name.");
      strcpy(friendlyName, "Default Sensor"); // Set a default if empty
  }

  EEPROM.end();
  Serial.println("General config loaded.");
  Serial.print(" ADC Cal: "); Serial.println(adcCalibration);
  Serial.print(" Temp Offset: "); Serial.println(sensorCalOffsetTemp);
  Serial.print(" Humid Offset: "); Serial.println(sensorCalOffsetHumidity);
  Serial.print(" Publish Interval: "); Serial.println(publishIntervalMs);
  Serial.print(" Friendly Name: "); Serial.println(friendlyName);
}

void saveGeneralConfig() {
  Serial.println("Saving general config to EEPROM...");
  EEPROM.begin(EEPROM_SIZE);
  EEPROM.put(EEPROM_ADDR_ADC_CAL, adcCalibration);
  EEPROM.put(EEPROM_ADDR_TEMP_CAL, sensorCalOffsetTemp);
  EEPROM.put(EEPROM_ADDR_HUMID_CAL, sensorCalOffsetHumidity);
  EEPROM.put(EEPROM_ADDR_PUBLISH_INTERVAL, publishIntervalMs);

  // Write friendly name string
  for (int i = 0; i < FRIENDLY_NAME_MAXLEN - 1; i++) {
    EEPROM.write(EEPROM_ADDR_NAME + i, friendlyName[i]);
    if (friendlyName[i] == '\0') break; // Stop writing after null terminator
  }
  EEPROM.write(EEPROM_ADDR_NAME + FRIENDLY_NAME_MAXLEN - 1, '\0'); // Ensure last byte is null term

  if (EEPROM.commit()) {
    Serial.println("EEPROM general config saved successfully.");
  } else {
    Serial.println("ERROR: EEPROM commit failed!");
  }
  EEPROM.end();
}

void clearEEPROM() {
  Serial.println("Clearing EEPROM due to corruption...");
  EEPROM.begin(EEPROM_SIZE);

  // Clear all EEPROM data
  for (int i = 0; i < EEPROM_SIZE; i++) {
    EEPROM.write(i, 0xFF);
  }

  if (EEPROM.commit()) {
    Serial.println("EEPROM cleared successfully.");
  } else {
    Serial.println("ERROR: EEPROM clear failed!");
  }
  EEPROM.end();

  // Reset to defaults
  adcCalibration = 1.0;
  sensorCalOffsetTemp = 0.0;
  sensorCalOffsetHumidity = 0.0;
  publishIntervalMs = PUBLISH_INTERVAL_DEFAULT_MS;
  strcpy(friendlyName, "Type101Sensor");

  // Save defaults
  saveGeneralConfig();
}


// === CONFIG PORTAL ===
void startConfigPortal() {
  configModeActive = true;
  Serial.println("Starting WiFi Configuration Portal...");

  WiFi.mode(WIFI_AP);
  String apName = "SensorSetup_" + String(nodeID); // Create unique AP name
  Serial.print("AP Name: "); Serial.println(apName);

  // Start AP
  if(WiFi.softAP(apName.c_str())) {
     Serial.print("AP IP address: "); Serial.println(WiFi.softAPIP());

    // Start DNS Server for captive portal
    if(dnsServer.start(53, "*", WiFi.softAPIP())) {
        Serial.println("DNS Server Started.");
    } else {
        Serial.println("Failed to start DNS Server!");
    }

    // --- Web Server Handlers ---
    webServer.on("/", HTTP_GET, []() {
      String html = R"(
        <html><head><title>Sensor WiFi Setup</title>
        <style>body{font-family: sans-serif;} label{display: block; margin-bottom: 5px;} input{width: 90%; padding: 8px; margin-bottom: 10px;}</style>
        </head><body><h2>Configure WiFi Credentials</h2>
        <form action='/save' method='POST'>
          <label for='ssid'>SSID:</label><input id='ssid' name='ssid'><br>
          <label for='pass'>Password:</label><input id='pass' name='pass' type='password'><br><br>
          <input type='submit' value='Save & Reboot'>
        </form></body></html>)";
      webServer.send(200, "text/html", html);
    });

    webServer.on("/save", HTTP_POST, []() {
      String newSSID = webServer.arg("ssid");
      String newPASS = webServer.arg("pass");
      Serial.println("Received new WiFi credentials via web form.");
      Serial.print(" New SSID: "); Serial.println(newSSID);
      // Avoid printing password: Serial.print(" New Pass: "); Serial.println(newPASS);

      saveWiFiCredentials(newSSID, newPASS); // Save to EEPROM

      String html = R"(
        <html><head><title>Saved</title></head><body>
        <h2>WiFi Credentials Saved!</h2><p>Device will now reboot and attempt to connect.</p>
        </body></html>)";
      webServer.send(200, "text/html", html);

      delay(2000); // Give browser time to receive response
      ESP.restart(); // Reboot to apply new settings
    });

    // --- Captive Portal Handlers ---
    // Android redirects
    webServer.on("/generate_204", HTTP_GET, []() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });
    // iOS redirects
    webServer.on("/hotspot-detect.html", HTTP_GET, []() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });
    webServer.on("/captive.apple.com", HTTP_GET, []() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });
    // Microsoft redirects
    webServer.on("/fwlink", HTTP_GET, []() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });
    // Generic redirect for anything else
    webServer.onNotFound([]() { webServer.sendHeader("Location", "/", true); webServer.send(302, "text/plain", ""); });

    webServer.begin();
    Serial.println("Web Server started.");

    if (displayOK) {
        display.clearDisplay();
        display.setTextSize(1);
        display.setCursor(0, 0);
        display.println("CONFIG MODE ACTIVE");
        display.setCursor(0, 10);
        display.print("Connect to AP:");
        display.setCursor(0, 20);
        display.println(apName);
        display.display();
    }

  } else {
      Serial.println("Failed to start Soft AP!");
      if (displayOK) {
          display.clearDisplay();
          display.setTextSize(1);
          display.setCursor(0, 10);
          display.println("AP Start Failed!");
          display.display();
      }
      // Maybe retry or signal error state
  }
}

void handleWebServer() {
  if(configModeActive) {
    webServer.handleClient();
  }
}

void handleDns() {
    if(configModeActive) {
        dnsServer.processNextRequest();
    }
}

// === WIFI ===
void setupWiFi() {
  generateNodeID(); // Generate the unique ID first
  loadWiFiCredentials(); // Load credentials from EEPROM or defaults

  WiFi.mode(WIFI_STA); // Set to Station mode
  WiFi.hostname(nodeID); // Set hostname to NodeID (optional, good for network scanning)

  Serial.print("Attempting to connect to WiFi SSID: ");
  Serial.println(wifi_ssid);

  if (displayOK) {
    display.clearDisplay();
    display.setTextSize(1);
    display.setCursor(0, 0);
    display.println("Connecting WiFi:");
    display.setCursor(0, 10);
    display.println(wifi_ssid);
    // Optionally add connecting animation here
    display.display();
  }

  delay(100); // Short delay before starting connection attempt
  WiFi.begin(wifi_ssid, wifi_pass);
  WiFi.setAutoReconnect(true); // Automatically reconnect if connection drops
  WiFi.persistent(true);    // Store credentials in SDK flash (redundant if using EEPROM but doesn't hurt)

  Serial.print("Connecting...");
  unsigned long startAttemptTime = millis();
  while (WiFi.status() != WL_CONNECTED && (millis() - startAttemptTime < WIFI_CONNECT_TIMEOUT_MS)) {
    Serial.print(".");
    delay(500);
    // Could update a connecting animation on the display here
  }
  Serial.println();

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi Connected!");
    Serial.print("IP Address: "); Serial.println(WiFi.localIP());
    Serial.print("RSSI: "); Serial.println(WiFi.RSSI());
    if (displayOK) {
      display.clearDisplay();
      display.setTextSize(1);
      display.setCursor(0, 0);
      display.println("WiFi Connected!");
      display.setCursor(0, 10);
      display.println(WiFi.localIP());
      display.setCursor(0, 20);
      display.print("RSSI: "); display.print(WiFi.RSSI()); display.println(" dBm");
      display.display();
      delay(2000); // Show connection success message briefly
    }
  } else {
    Serial.println("WiFi Connection Failed after timeout.");
    if (displayOK) {
      display.clearDisplay();
      display.setTextSize(1);
      display.setCursor(0, 0);
      display.println("WiFi Failed!");
      display.setCursor(0, 10);
      display.println("Starting AP Mode...");
      display.display();
      delay(2000);
    }
    startConfigPortal(); // Fallback to AP mode for configuration
  }
}

// === AWS IOT CORE ===
void setupAWS() {
  telemTopic = "sensors/pi_aggregator_adcbc1/readings";
  configTopic = "config/pi_aggregator_adcbc1/set";
  controlTopic = "control/pi_aggregator_adcbc1/#";

  Serial.print("Telemetry Topic: "); Serial.println(telemTopic);
  Serial.print("Config Topic: "); Serial.println(configTopic);
  Serial.print("Control Topic: "); Serial.println(controlTopic);

  // Load certs from PROGMEM
  BearSSL::X509List cert((const uint8_t*)certificate_pem_crt, strlen_P(certificate_pem_crt));
  BearSSL::X509List ca((const uint8_t*)amazon_ca_cert, strlen_P(amazon_ca_cert));
  BearSSL::PrivateKey key((const uint8_t*)private_pem_key, strlen_P(private_pem_key));

  awsClient.setTrustAnchors(&ca);
  awsClient.setClientRSACert(&cert, &key);
  awsClient.setSync(true);                  // Force handshake in main task
  awsClient.setBufferSizes(512, 512);       // Smaller BearSSL buffers
  awsClient.setTimeout(30000);              // TLS timeout
  awsClient.setNoDelay(true);               // Disable Nagle's algorithm

  mqttClient.set_server(aws_endpoint, aws_port);
  mqttClient.set_callback(mqttCallback);
  // Note: Using Imroy PubSubClient methods

  Serial.print("BearSSL client mode: ");
  Serial.println("TLS certificates configured.");
  Serial.print("  CA certs loaded: "); Serial.println(ca.getCount());
  Serial.print("  Client cert loaded: "); Serial.println(cert.getCount());
  Serial.println("AWS IoT Core certificates and MQTT client configured.");
  Serial.print("AWS Endpoint: "); Serial.println(aws_endpoint);
  Serial.print("AWS Port: "); Serial.println(aws_port);
}


void connectAWS() {
  if (mqttClient.connected()) {
    awsConnected = true;
    return;
  }

  unsigned long now = millis();
  if (now - lastAwsAttemptTimestamp < AWS_RECONNECT_INTERVAL_MS) return;
  lastAwsAttemptTimestamp = now;

  Serial.print("Free heap before connection: "); Serial.println(ESP.getFreeHeap());
  Serial.print("Attempting AWS IoT MQTT connection... ClientID: ");
  String clientId = "pi_aggregator_adcbc1";
  Serial.println(clientId);

  // Feed watchdog during connection attempt
  ESP.wdtFeed();

  awsClient.flush();
  awsClient.stop();
  delay(100);
  ESP.wdtFeed(); // Feed watchdog again

  bool connected = mqttClient.connect(clientId.c_str());

  ESP.wdtFeed(); // Feed watchdog after connection attempt

  if (connected) {
    Serial.println("AWS IoT MQTT Connected!");
    awsConnected = true;
    ESP.wdtFeed(); // Feed watchdog during subscription

    if (mqttClient.subscribe(configTopic.c_str())) {
      Serial.print("Subscribed to config topic: "); Serial.println(configTopic);
    } else {
      Serial.println("ERROR: Failed to subscribe to config topic!");
    }
    ESP.wdtFeed(); // Feed watchdog between subscriptions

    if (mqttClient.subscribe(controlTopic.c_str())) {
      Serial.print("Subscribed to control topic: "); Serial.println(controlTopic);
    } else {
      Serial.println("ERROR: Failed to subscribe to control topic!");
    }

  } else {
    Serial.println("AWS MQTT connect failed!");
    Serial.print("MQTT error: "); Serial.println(mqttClient.connected() ? 0 : -1); // replaced .state()
    awsConnected = false;
  }
}





// MQTT callback function for processing incoming messages
void mqttCallback(const MQTT::Publish& pub) {
  Serial.print("Message arrived ["); Serial.print(pub.topic()); Serial.print("] ");

  // Get payload as string
  String payload = pub.payload_string();
  Serial.println(payload);

  // Check if message is on config topic
  if (String(pub.topic()) == configTopic) {
    processConfigMessage(payload);
  } else if (String(pub.topic()).startsWith(controlTopic.substring(0, controlTopic.length() - 1))) { // Remove the # wildcard for comparison
    Serial.println("Control message received (not yet implemented)");
    // TODO: Implement control message handling
  } else {
    Serial.println("Message on unknown topic - ignoring");
  }
}

// === NTP TIME SETUP ===
void setupNTP() {
  Serial.println("Starting NTP client...");
  timeClient.begin();

  // Try multiple times to get accurate time
  int attempts = 0;
  while (!timeClient.update() && attempts < 10) {
    delay(1000);
    attempts++;
    Serial.print(".");
  }
  Serial.println();

  if (timeClient.isTimeSet()) {
    Serial.print("NTP time synchronized: ");
    Serial.println(timeClient.getFormattedTime());
    Serial.print("Epoch time: "); Serial.println(timeClient.getEpochTime());

    // Set system time for certificate validation
    time_t now = timeClient.getEpochTime();
    struct timeval tv = { now, 0 };
    settimeofday(&tv, nullptr);
    Serial.println("System time set for certificate validation");
  } else {
    Serial.println("Failed to sync NTP time - TLS may fail!");
  }
}

String getISOTimestamp() {
  timeClient.update();
  unsigned long epochTime = timeClient.getEpochTime();

  // Convert to ISO 8601 format: 2025-06-15T01:23:45Z
  time_t rawTime = epochTime;
  struct tm* timeInfo = gmtime(&rawTime);

  char buffer[25];
  strftime(buffer, sizeof(buffer), "%Y-%m-%dT%H:%M:%SZ", timeInfo);
  return String(buffer);
}




// === SENSOR ===
void setupSensor() {
  Serial.println("Initializing AM2320 sensor...");
  am2320.begin();
  // No easy return value check, we'll rely on isnan() checks later
  delay(100); // Small delay after init
}

void updateSensorReadings() {
  unsigned long now = millis();
  // Read sensor only at the specified interval
  if (now - lastSensorUpdateTimestamp >= SENSOR_READ_INTERVAL_MS) {
    lastSensorUpdateTimestamp = now;

    Serial.println("Reading sensors...");

    // Read Temperature and Humidity from AM2320
    float tempC = am2320.readTemperature(); // Reads temperature in Celsius
    float humidity = am2320.readHumidity();

    // Read Analog Input (A0) and convert to voltage (0-3.3V)
    int adcRaw = analogRead(A0);
    lastVoltage = (adcRaw / 1023.0) * 3.3 * adcCalibration; // Convert to voltage with calibration

    // Read binary inputs from D5 and D6
    readBinaryInputs();

    // Validate AM2320 readings (check for NAN - Not A Number)
    if (isnan(tempC) || isnan(humidity)) {
      Serial.println("Failed to read from AM2320 sensor!");
      // Keep previous valid readings or explicitly set to NAN? Decide based on requirements.
      // Setting to NAN ensures bad data isn't displayed or published.
      // lastTempC = NAN; // Uncomment if you want failed reads to clear old values
      // lastHumidity = NAN;
    } else {
      // Readings seem valid, update the global variables
      lastTempC = tempC;
      lastHumidity = humidity;
      Serial.print("  Temp: "); Serial.print(lastTempC); Serial.print(" °C");
      Serial.print("  Humidity: "); Serial.print(lastHumidity); Serial.print(" %");
      Serial.print("  ADC Raw: "); Serial.print(adcRaw);
      Serial.print("  Voltage: "); Serial.print(lastVoltage); Serial.print(" V");
      Serial.print("  Binary: "); Serial.println(lastBinaryInput);
    }
  }
}

void readBinaryInputs() {
  // Read D5 (GPIO14) and D6 (GPIO12) as binary inputs
  // D6 = MSB, D5 = LSB, so binary value = (D6 << 1) | D5
  int d5_state = digitalRead(BINARY_INPUT_D5);
  int d6_state = digitalRead(BINARY_INPUT_D6);

  lastBinaryInput = (d6_state << 1) | d5_state;

  // Debug output
  Serial.print("  D5: "); Serial.print(d5_state);
  Serial.print("  D6: "); Serial.print(d6_state);
  Serial.print("  Binary: "); Serial.print(lastBinaryInput);
}

// === TELEMETRY PUBLISHING ===
void publishTelemetry() {
  // Only publish if we have valid sensor readings
  if (isnan(lastTempC) || isnan(lastHumidity)) {
    Serial.println("Skipping telemetry publish: Invalid sensor data.");
    return;
  }
  if (!awsConnected || !mqttClient.connected()) {
    Serial.println("Skipping telemetry publish: AWS MQTT not connected.");
    return;
  }

  StaticJsonDocument<512> doc;

  // Sensor values
  doc["temperature"] = round((lastTempC + sensorCalOffsetTemp) * 10) / 10.0; // Round to 1 decimal
  doc["humidity"] = round((lastHumidity + sensorCalOffsetHumidity) * 10) / 10.0; // Round to 1 decimal
  doc["voltage"] = round(lastVoltage * 100) / 100.0; // Round to 2 decimals
  doc["binary"] = lastBinaryInput;
  doc["timestamp"] = getISOTimestamp();

  // Serialize JSON payload to a buffer
  char payloadBuffer[512];
  serializeJson(doc, payloadBuffer);

  Serial.print("Publishing telemetry to AWS IoT pub.topic() ["); Serial.print(telemTopic); Serial.print("]: ");
  Serial.println(payloadBuffer);

  // Publish to AWS IoT Core via MQTT over TLS
  if (mqttClient.publish(telemTopic.c_str(), payloadBuffer)) {
    Serial.println("Telemetry published successfully to AWS IoT Core!");
    lastPublishTimestamp = millis(); // Update timestamp only on successful publish
  } else {
    Serial.println("ERROR: Failed to publish telemetry to AWS IoT Core!");
    Serial.print("MQTT State: "); Serial.println(mqttClient.connected() ? 0 : -1); // replaced .state()
    // Don't update timestamp on failure - will retry on next cycle
  }
}

void processConfigMessage(const String& payload) {
  Serial.println("Processing config message...");
  StaticJsonDocument<256> doc;
  DeserializationError error = deserializeJson(doc, payload);

  if (error) {
    Serial.print("deserializeJson() failed: ");
    Serial.println(error.c_str());
    return;
  }

  bool configChanged = false;

  // Check for interval configuration
  if (doc.containsKey("interval")) {
    unsigned long newInterval = doc["interval"];
    newInterval *= 1000; // Convert seconds to milliseconds

    if (newInterval >= 5000 && newInterval <= 3600000) { // 5sec to 1hr limit
      if (newInterval != publishIntervalMs) {
        Serial.print("Updating publish interval: "); Serial.print(newInterval/1000); Serial.println(" seconds");
        publishIntervalMs = newInterval;
        configChanged = true;
      }
    } else {
      Serial.print("Ignoring invalid interval: "); Serial.println(newInterval/1000);
    }
  }

  // Save configuration if changed
  if (configChanged) {
    saveGeneralConfig();
  } else {
    Serial.println("No configuration changes detected.");
  }
}



// Helper to get the correct WiFi icon based on signal level
const uint8_t* getWifiIcon(int level, uint8_t &width) {
  width = 8; // All icons are 8 pixels wide
  switch (level) {
    case 1: return wifi_icon_1;
    case 2: return wifi_icon_2;
    case 3: return wifi_icon_3;
    case 4: return wifi_icon_4;
    case 5: return wifi_icon_5;
    case 6: return wifi_icon_6;
    default: return wifi_icon_0; // Level 0 or unknown
  }
}

// Convert RSSI value to a signal level (0-6)
int wifiLevelFromRSSI(long rssi) {
  if (WiFi.status() != WL_CONNECTED) return 0; // No connection = level 0
  if (rssi >= -55) return 6; // Excellent
  else if (rssi >= -65) return 5; // Good
  else if (rssi >= -70) return 4; // Fair
  else if (rssi >= -75) return 3; // Okay
  else if (rssi >= -85) return 2; // Weak
  else return 1;                  // Very Weak
}

void setupDisplay() {
  Serial.println("Initializing OLED display...");
  // Address 0x3C for 128x32
  if (display.begin(SSD1306_SWITCHCAPVCC, 0x3C)) {
    displayOK = true;
    Serial.println("OLED display initialized successfully.");
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("Initializing...");
    display.display();
    delay(500); // Show init message briefly
  } else {
    displayOK = false;
    Serial.println("ERROR: OLED display initialization failed!");
  }
}

// Draw the top status bar (WiFi, NodeID, MQTT Status)
void drawTopStatusBar() {
  if (!displayOK) return;

  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE); // Ensure text is white
  display.fillRect(0, 0, SCREEN_WIDTH, 9, SSD1306_BLACK); // Clear status bar area

  // --- WiFi Status ---
  uint8_t iconWidth = 8;
  long currentRSSI = (WiFi.status() == WL_CONNECTED) ? WiFi.RSSI() : -100; // Get RSSI or default
  int wifiLevel = wifiLevelFromRSSI(currentRSSI);
  const uint8_t* wifiIcon = getWifiIcon(wifiLevel, iconWidth);
  display.drawBitmap(0, 0, wifiIcon, iconWidth, 8, SSD1306_WHITE);
  // Optionally display RSSI value next to icon if space allows

  // --- Node ID ---
  display.setCursor((SCREEN_WIDTH / 2) - (strlen(nodeID) * 6 / 2), 0); // Center NodeID roughly
  display.print(nodeID);

  // --- AWS IoT Status ---
  const uint8_t* awsStatusIcon = (awsConnected && mqttClient.connected()) ? checkmark_icon : x_icon;
  display.drawBitmap(SCREEN_WIDTH - 18, 0, mqtt_icon, 8, 8, SSD1306_WHITE); // Cloud icon for AWS
  display.drawBitmap(SCREEN_WIDTH - 8, 0, awsStatusIcon, 8, 8, SSD1306_WHITE); // Check or X
}

// Draw the progress bar at the bottom showing time until next publish
void drawPublishProgressBar() {
  if (!displayOK) return;
  display.fillRect(0, SCREEN_HEIGHT - 2, SCREEN_WIDTH, 2, SSD1306_BLACK); // Clear bar area

  unsigned long now = millis();
  // Calculate elapsed time since last *successful* publish
  unsigned long elapsed = (lastPublishTimestamp == 0) ? 0 : (now - lastPublishTimestamp);
  if (elapsed > publishIntervalMs) {
      elapsed = publishIntervalMs; // Cap at max interval
  }

  // Map elapsed time to screen width for the progress bar
  int barWidth = map(elapsed, 0, publishIntervalMs, 0, SCREEN_WIDTH);
  display.fillRect(0, SCREEN_HEIGHT - 1, barWidth, 1, SSD1306_WHITE); // Draw bar
}

// Main function to update the entire display content
void updateDisplay() {
  if (!displayOK || configModeActive) return; // Don't update display in config mode

  unsigned long now = millis();
  if (now - lastDisplayUpdateTimestamp >= DISPLAY_UPDATE_INTERVAL_MS) {
    lastDisplayUpdateTimestamp = now;

    display.clearDisplay(); // Clear previous content

    drawTopStatusBar(); // Draw WiFi, NodeID, MQTT status

    // --- Sensor Data Area ---
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 10); // Position below status bar

    if (isnan(lastTempC) || isnan(lastHumidity)) {
      // Display Sensor Error Message
      display.drawBitmap(0, 12, sensor_icon, 8, 8, SSD1306_WHITE);
      display.setTextSize(2);
      display.setCursor(12, 12); // Position next to icon
      display.println(F("Sensor")); // Use F() macro for static strings
      display.setCursor(12, 24);
      display.println(F("Error!"));
    } else {
      // Display Valid Sensor Readings
      // Temperature
      display.drawBitmap(0, 11, sensor_icon, 8, 8, SSD1306_WHITE); // Thermometer icon
      display.setCursor(10, 10);
      display.printf("T:%.1f", lastTempC + sensorCalOffsetTemp); // Apply offset for display
      int tempX = display.getCursorX();
      int tempY = display.getCursorY();
      display.drawCircle(tempX + 2, tempY + 0, 1, SSD1306_WHITE); // Degree symbol °
      display.print("C");

      // Humidity
      display.setCursor(SCREEN_WIDTH / 2 + 5, 10); // Position humidity on the right
      display.printf("H:%.1f%%", lastHumidity + sensorCalOffsetHumidity); // Apply offset

      // Voltage and Binary Input
      display.setCursor(10, 22); // Below Temperature
      display.printf("V:%.2fV", lastVoltage);

      // Binary input display (show as 2-bit binary: 00, 01, 10, 11)
      display.setCursor(SCREEN_WIDTH / 2 + 5, 22); // Right side, below humidity
      display.printf("B:%d%d", (lastBinaryInput >> 1) & 1, lastBinaryInput & 1);
    }

    drawPublishProgressBar(); // Draw the progress bar at the bottom

    display.display(); // Send buffer to the OLED
  }
}

// === SETUP ===
void setup() {
  Serial.begin(115200); // Use a faster baud rate
  #ifdef DEBUG_BEARSSL
  Serial.println("DEBUG_BEARSSL is active");
  #else
    Serial.println("DEBUG_BEARSSL is NOT active");
  #endif
  Serial.print("ESP8266 Core version: ");
  Serial.println(ESP.getCoreVersion());
  Serial.println();
  Serial.println("=======================================");
  Serial.print("Starting ESP8266 AWS IoT Sensor v"); Serial.println(FIRMWARE_VERSION);
  Serial.println("=======================================");

  // Initialize digital input pins
  pinMode(BINARY_INPUT_D5, INPUT_PULLUP);
  pinMode(BINARY_INPUT_D6, INPUT_PULLUP);

  Wire.begin(SDA_PIN, SCL_PIN); // Initialize I2C

  setupDisplay(); // Initialize OLED Display first for feedback
  setupSensor(); // Initialize AM2320 Sensor
  loadGeneralConfig(); // Load settings like calibration, interval, name

  // Check for EEPROM corruption and clear if needed
  if (isnan(adcCalibration) || adcCalibration < 0.1 || adcCalibration > 10.0 ||
      strlen(friendlyName) == 0 || friendlyName[0] == 0xFF) {
    Serial.println("EEPROM corruption detected, clearing and resetting to defaults...");
    clearEEPROM();
  }

  setupWiFi(); // Initialize WiFi (connects or starts AP)

  // Only proceed with AWS and NTP setup if WiFi connected successfully
  if (WiFi.status() == WL_CONNECTED) {
    setupNTP(); // Setup NTP time synchronization
    setupAWS(); // Setup AWS IoT Core connection
  } else {
    Serial.println("Skipping AWS IoT and NTP setup - WiFi not connected.");
  }

  Serial.println("Setup Complete. Starting main loop...");
}

// === MAIN LOOP ===
void loop() {
  // Handle Web Server and DNS requests only when in AP Config Mode
  if (configModeActive) {
    handleDns();
    handleWebServer();
    // Optionally update display to show AP mode status periodically
    // No sensor reads or AWS handling in this mode
    return; // Exit loop early if in config mode
  }

  // --- Normal Operation Mode ---

  // 1. Ensure WiFi is connected (ESP core handles auto-reconnect)
  //    Could add a check here to reboot if WiFi remains disconnected for too long

  // 2. Ensure AWS IoT is connected
  if (WiFi.status() == WL_CONNECTED) {
      connectAWS(); // Attempt to connect/reconnect if needed
  }

  // 3. Process MQTT messages
  mqttClient.loop(); // Must be called regularly to process incoming messages

  // 4. Update NTP time periodically
  timeClient.update();

  // 5. Read sensor data periodically
  updateSensorReadings();

  // 6. Update the display periodically
  updateDisplay();

  // 7. Publish telemetry data periodically
  unsigned long now = millis();
  if (awsConnected && mqttClient.connected() && (now - lastPublishTimestamp >= publishIntervalMs)) {
      // Note: lastPublishTimestamp is updated inside publishTelemetry on success
      publishTelemetry();
  }

  // Yield for ESP8266 background tasks (important!)
  yield();
}
