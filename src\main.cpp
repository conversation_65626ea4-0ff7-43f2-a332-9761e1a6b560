/*
 * ESP32 Multi-Board Sensor Firmware with Complete Provisioning System
 *
 * A comprehensive IoT device platform with full lifecycle management,
 * provisioning, command processing, and EEPROM configuration storage.
 *
 * Features:
 * - Complete device lifecycle (newborn -> adult)
 * - Dual MQTT broker support (provisioning + AWS IoT)
 * - 70+ configuration parameters with EEPROM persistence
 * - Full command system with acknowledgments
 * - Base62 device ID generation
 * - Sensor telemetry with calibration
 * - OLED display with status and messages
 *
 * Author: Generated for ESP32 sensor project with complete provisioning
 * Version: 2.0 - Complete Implementation
 */

#include <Arduino.h>
#include "config.h"

// Core ESP32 libraries
#include <WiFi.h>
#include <WiFiClient.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <Wire.h>
#include <EEPROM.h>

// Time libraries
#include <WiFiUdp.h>
#include <NTPClient.h>
#include <time.h>

// Sensor libraries
#include <Adafruit_AM2320.h>

// Display libraries (if available)
#if HAS_OLED
  #include <Adafruit_GFX.h>
  #include <Adafruit_SSD1306.h>
  #include "icons.h"
#endif

// ===== EXTERNAL FUNCTION DECLARATIONS =====
// From eeprom_config.cpp
void initializeEEPROM();
bool loadConfigurationFromEEPROM();
void saveConfigurationToEEPROM();
void resetConfigurationToDefaults();
void eraseEEPROM();
void printConfiguration();

// From device_lifecycle.cpp
void initializeLifecycle();
void transitionToStatus(device_status_t newStatus, const char* reason = nullptr);
void handleConnectionFailure();
void handleConfigurationReceived();
void handleSuccessfulConnection();
bool shouldRetryConnection();
bool isOperationalStatus();
bool isProvisioningStatus();
bool needsProvisioning();
bool needsVerification();
bool hasIssues();
void printLifecycleStatus();

// From mqtt_manager.cpp
void initializeMQTT();
void setupMQTTForCurrentStatus();
bool connectMQTT();
void subscribeToTopics();
void disconnectMQTT();
bool isMQTTConnected();
void loopMQTT();
bool publishMessage(const char* topic, const char* payload, bool retain = false);
String getConfigTopic();
String getCommandTopic();
String getTelemetryTopic();
String getCommandAckTopic();
bool isUsingAWSMQTT();
void printMQTTStatus();

// From provisioning_handler.cpp
bool publishHelloMessage();
bool publishTelemetryMessage();
bool processConfigurationMessage(const String& payload);
bool validateConfiguration();

// From command_processor.cpp
bool processCommandMessage(const String& payload);
bool sendCommandAck(const String& command, bool success, const String& message = "");
bool isInSuspendMode();
String getSuspendMessage();
String getCurrentUIMessage();
bool hasActiveUIMessage();
void clearUIMessage();
void updateDisplayWithMessages();
bool shouldPublishTelemetry();
void printCommandStatus();
void mqttMessageCallback(char* topic, byte* payload, unsigned int length);

// ===== GLOBAL VARIABLES =====

// Device configuration (loaded from EEPROM)
device_config_t config;

// Device identification
String deviceId;
String macAddress;

// Network objects
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, NTP_SERVER, NTP_OFFSET, NTP_UPDATE_INTERVAL);

// Sensor objects
Adafruit_AM2320 am2320;

// Display object (if available)
#if HAS_OLED
  Adafruit_SSD1306 display(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);
#endif

// System state variables
bool wifiConnected = false;
bool ntpSynced = false;
unsigned long lastTelemetryTime = 0;
unsigned long lastSensorReadTime = 0;
unsigned long lastDisplayUpdateTime = 0;
unsigned long lastConnectionAttempt = 0;
uint8_t connectionFailures = 0;

// Sensor data structure
struct SensorData {
  float temperature = 0.0;
  float humidity = 0.0;
  float adcVoltage = 0.0;
  uint8_t binaryState = 0;
  unsigned long timestamp = 0;
  String connectionType = "none";
};

SensorData currentSensorData;

// ===== LOCAL FUNCTION PROTOTYPES =====
void setupSystem();
void generateDeviceId();
String encodeBase62(uint64_t value, int length);
void setupWiFi();
void setupNTP();
void setupSensors();
void setupDisplay();
void readSensors();
void updateDisplay();
void mainLoop();

// ===== MAIN FUNCTIONS =====

void setup() {
  Serial.begin(SERIAL_BAUD);
  delay(1000);

  DEBUG_PRINTLN("\n===== ESP32 Sensor with Complete Provisioning System =====");
  DEBUG_PRINTF("Board: %s\n", BOARD_NAME);
  DEBUG_PRINTF("Firmware: %s\n", FIRMWARE_VERSION);
  DEBUG_PRINTF("Hardware: %s\n", HARDWARE_VERSION);
  DEBUG_PRINTF("Free heap at start: %d bytes\n", ESP.getFreeHeap());

  // Initialize core systems
  setupSystem();

  // Load configuration from EEPROM
  initializeEEPROM();
  if (!loadConfigurationFromEEPROM()) {
    DEBUG_PRINTLN("Loading default configuration...");
    resetConfigurationToDefaults();
  }

  // Initialize lifecycle management
  initializeLifecycle();

  // Initialize hardware
  setupSensors();
  setupDisplay();

  // Initialize networking
  setupWiFi();
  if (wifiConnected) {
    setupNTP();
  }

  // Initialize MQTT
  initializeMQTT();
  setupMQTTForCurrentStatus();

  // Print system status
  DEBUG_PRINTF("Device ID: %s\n", deviceId.c_str());
  DEBUG_PRINTF("MAC Address: %s\n", macAddress.c_str());
  DEBUG_PRINTF("Status: %s (%d)\n", getStatusName(config.status_code), config.status_code);
  DEBUG_PRINTF("Free heap after setup: %d bytes\n", ESP.getFreeHeap());

  printConfiguration();
  printLifecycleStatus();
  printMQTTStatus();

  DEBUG_PRINTLN("Setup complete. Starting main loop...");
}

void loop() {
  mainLoop();
}

void mainLoop() {
  unsigned long now = millis();

  // Read sensors periodically
  if (now - lastSensorReadTime > config.sensor_read_interval_ms) {
    readSensors();
    lastSensorReadTime = now;
  }

  // Update display periodically
  #if HAS_OLED
  if (now - lastDisplayUpdateTime > config.display_refresh_interval_ms) {
    updateDisplay();
    lastDisplayUpdateTime = now;
  }
  #endif

  // Handle MQTT connection and messaging
  if (isMQTTConnected()) {
    loopMQTT();
  } else {
    // Try to reconnect if enough time has passed
    if (shouldRetryConnection()) {
      DEBUG_PRINTLN("Attempting MQTT reconnection...");
      connectMQTT();
    }
  }

  // Publish messages based on status and suspend mode
  if (isMQTTConnected() && shouldPublishTelemetry()) {
    if (isOperationalStatus()) {
      // Operational mode: publish telemetry
      if (now - lastTelemetryTime > (config.publish_interval_sec * 1000)) {
        publishTelemetryMessage();
        lastTelemetryTime = now;
      }
    } else if (isProvisioningStatus()) {
      // Provisioning mode: publish hello messages
      if (now - lastTelemetryTime > 30000) {  // Every 30 seconds
        publishHelloMessage();
        lastTelemetryTime = now;
      }
    }
  }

  delay(100); // Small delay to prevent tight loop
}

// ===== IMPLEMENTATION FUNCTIONS =====

void setupSystem() {
  DEBUG_PRINTLN("Initializing system...");

  // Initialize built-in LED
  pinMode(STATUS_LED_PIN, OUTPUT);
  digitalWrite(STATUS_LED_PIN, LOW);

  // Initialize digital inputs with pullups
  pinMode(DIN1_PIN, INPUT_PULLUP);
  pinMode(DIN2_PIN, INPUT_PULLUP);

  // Initialize I2C
  Wire.begin(I2C_SDA, I2C_SCL);

  // Generate unique device ID from MAC address
  generateDeviceId();

  DEBUG_PRINTLN("System initialization complete.");
}

void generateDeviceId() {
  // Get MAC address
  uint8_t mac[6];
  WiFi.macAddress(mac);

  // Store MAC as string for reference
  macAddress = WiFi.macAddress();

  // Convert MAC to 48-bit integer
  uint64_t macInt = 0;
  for (int i = 0; i < 6; i++) {
    macInt = (macInt << 8) | mac[i];
  }

  // Encode as 6-character base62 string
  deviceId = encodeBase62(macInt, 6);

  DEBUG_PRINTF("MAC Address: %s\n", macAddress.c_str());
  DEBUG_PRINTF("Device ID: %s\n", deviceId.c_str());
}

String encodeBase62(uint64_t value, int length) {
  String result = "";

  if (value == 0) {
    result = "0";
  } else {
    while (value > 0) {
      result = base62_chars[value % 62] + result;
      value /= 62;
    }
  }

  // Pad with leading zeros if needed
  while (result.length() < length) {
    result = "0" + result;
  }

  return result.substring(0, length);
}

void setupWiFi() {
  DEBUG_PRINTF("Connecting to WiFi: %s\n", config.wifi_ssid);

  WiFi.mode(WIFI_STA);

  if (config.use_static_ip) {
    IPAddress ip(config.static_ip);
    IPAddress gateway(config.static_gateway);
    IPAddress subnet(config.static_subnet);
    IPAddress dns1(config.static_dns1);
    IPAddress dns2(config.static_dns2);

    if (!WiFi.config(ip, gateway, subnet, dns1, dns2)) {
      DEBUG_PRINTLN("Static IP configuration failed, falling back to DHCP");
      config.use_static_ip = false;
    }
  }

  WiFi.begin(config.wifi_ssid, config.wifi_pass);

  unsigned long startTime = millis();
  while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < WIFI_CONNECT_TIMEOUT) {
    delay(500);
    DEBUG_PRINT(".");
  }

  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    DEBUG_PRINTLN("\nWiFi connected!");
    DEBUG_PRINTF("IP: %s\n", WiFi.localIP().toString().c_str());
    DEBUG_PRINTF("RSSI: %d dBm\n", WiFi.RSSI());
    digitalWrite(STATUS_LED_PIN, HIGH);
  } else {
    wifiConnected = false;
    DEBUG_PRINTLN("\nWiFi connection failed!");
    digitalWrite(STATUS_LED_PIN, LOW);
  }
}

void setupNTP() {
  if (!wifiConnected) return;

  DEBUG_PRINTLN("Starting NTP client...");
  timeClient.begin();

  int attempts = 0;
  while (!timeClient.update() && attempts < 10) {
    delay(1000);
    attempts++;
    DEBUG_PRINT(".");
  }

  if (timeClient.isTimeSet()) {
    ntpSynced = true;
    time_t epochTime = timeClient.getEpochTime();
    DEBUG_PRINTLN("\nNTP time synchronized!");
    DEBUG_PRINTF("Current time: %s\n", timeClient.getFormattedTime().c_str());
  } else {
    ntpSynced = false;
    DEBUG_PRINTLN("\nNTP sync failed!");
  }
}

void setupAWS() {
  if (!wifiConnected) return;

  DEBUG_PRINTLN("Setting up AWS IoT...");
  DEBUG_PRINTF("Free heap before AWS setup: %d bytes\n", ESP.getFreeHeap());

  // Load certificates
  awsClient.setCACert(amazon_ca_cert);
  awsClient.setCertificate(certificate_pem_crt);
  awsClient.setPrivateKey(private_pem_key);

  // Configure MQTT client
  mqttClient.setServer(AWS_IOT_ENDPOINT, AWS_IOT_PORT);
  mqttClient.setCallback(mqttCallback);
  mqttClient.setBufferSize(MQTT_BUFFER_SIZE);

  DEBUG_PRINTF("Free heap after AWS setup: %d bytes\n", ESP.getFreeHeap());
  DEBUG_PRINTLN("AWS IoT setup complete.");
}

void setupSensors() {
  DEBUG_PRINTLN("Initializing sensors...");

  // Initialize AM2320 sensor
  if (am2320.begin()) {
    DEBUG_PRINTLN("AM2320 sensor initialized successfully.");
  } else {
    DEBUG_PRINTLN("Failed to initialize AM2320 sensor!");
  }

  DEBUG_PRINTLN("Sensor initialization complete.");
}

void setupDisplay() {
  #if HAS_OLED
  DEBUG_PRINTLN("Initializing OLED display...");

  if (display.begin(SSD1306_SWITCHCAPVCC, OLED_ADDRESS)) {
    DEBUG_PRINTLN("OLED display initialized successfully.");

    // Clear display and show startup message
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("ESP32 Sensor v2.0");
    display.println(BOARD_NAME);
    display.printf("ID: %s", deviceId.substring(0, 6).c_str());
    display.setCursor(0, 30);
    display.printf("Status: %d", currentStatus);
    display.display();

  } else {
    DEBUG_PRINTLN("Failed to initialize OLED display!");
  }
  #else
  DEBUG_PRINTLN("No OLED display available on this board.");
  #endif
}

void readSensors() {
  // Read AM2320 temperature and humidity
  float temp = am2320.readTemperature();
  float hum = am2320.readHumidity();

  if (!isnan(temp) && !isnan(hum)) {
    // Apply calibration
    if (config.temp_offset_first) {
      currentSensorData.temperature = (temp + config.temp_offset) * config.temp_multiplier;
    } else {
      currentSensorData.temperature = (temp * config.temp_multiplier) + config.temp_offset;
    }

    if (config.hum_offset_first) {
      currentSensorData.humidity = (hum + config.hum_offset) * config.hum_multiplier;
    } else {
      currentSensorData.humidity = (hum * config.hum_multiplier) + config.hum_offset;
    }

    // Convert temperature unit if needed
    if (config.temp_unit == 'F') {
      currentSensorData.temperature = (currentSensorData.temperature * 9.0 / 5.0) + 32.0;
    }
  }

  // Read ADC voltage
  int adcRaw = analogRead(ADC_PIN);
  float adcVoltage = (adcRaw / 4095.0) * 3.3; // ESP32 ADC reference is 3.3V

  // Apply ADC calibration
  if (config.adc_offset_first) {
    currentSensorData.adcVoltage = (adcVoltage + config.adc_offset) * config.adc_multiplier;
  } else {
    currentSensorData.adcVoltage = (adcVoltage * config.adc_multiplier) + config.adc_offset;
  }

  // Read digital inputs
  bool din1 = digitalRead(DIN1_PIN);
  bool din2 = digitalRead(DIN2_PIN);

  // Apply inversion if configured
  if (config.bin1_invert) din1 = !din1;
  if (config.bin2_invert) din2 = !din2;

  // Create 2-bit binary state
  currentSensorData.binaryState = (din2 << 1) | din1;

  // Set timestamp
  currentSensorData.timestamp = timeClient.getEpochTime();

  // Set connection type
  if (isMQTTConnected()) {
    if (isUsingAWSMQTT()) {
      currentSensorData.connectionType = "AWS-IoT";
    } else {
      currentSensorData.connectionType = "Provisioning";
    }
  } else if (wifiConnected) {
    currentSensorData.connectionType = "WiFi";
  } else {
    currentSensorData.connectionType = "none";
  }
}

void updateDisplay() {
  #if HAS_OLED
  // Check for command messages first
  updateDisplayWithMessages();

  // If no command messages, show normal display
  String currentMsg = getCurrentUIMessage();
  if (currentMsg.length() == 0 && !isInSuspendMode()) {
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);

    // Line 1: Device name or type
    display.setCursor(0, 0);
    if (strlen(config.ui_name) > 0) {
      display.printf("%s", config.ui_name);
    } else {
      display.printf("%s %s", config.device_type, deviceId.substring(0, 4).c_str());
    }

    // Line 2: Temperature
    display.setCursor(0, 10);
    display.printf("Temp: %.*f %c", config.temp_decimal_places,
                   currentSensorData.temperature, config.temp_unit);

    // Line 3: Humidity
    display.setCursor(0, 20);
    display.printf("Hum:  %.*f %%", config.hum_decimal_places,
                   currentSensorData.humidity);

    // Line 4: ADC
    display.setCursor(0, 30);
    display.printf("%s: %.*f V", config.adc_label, config.adc_decimal_places,
                   currentSensorData.adcVoltage);

    // Line 5: Digital inputs
    display.setCursor(0, 40);
    const char* bin1_label = (currentSensorData.binaryState & 1) ?
                             config.bin1_label_on : config.bin1_label_off;
    const char* bin2_label = (currentSensorData.binaryState & 2) ?
                             config.bin2_label_on : config.bin2_label_off;
    display.printf("%s %s", bin1_label, bin2_label);

    // Line 6: Status
    display.setCursor(0, 50);
    if (isMQTTConnected()) {
      display.printf("%s: OK", isUsingAWSMQTT() ? "AWS" : "PROV");
      display.drawBitmap(110, 50, wifi_icon_6, 8, 8, SSD1306_WHITE);
      display.drawBitmap(120, 50, checkmark_icon, 8, 8, SSD1306_WHITE);
    } else if (wifiConnected) {
      display.print("WiFi: OK");
      display.drawBitmap(110, 50, wifi_icon_4, 8, 8, SSD1306_WHITE);
      display.drawBitmap(120, 50, x_icon, 8, 8, SSD1306_WHITE);
    } else {
      display.print("No Connection");
      display.drawBitmap(110, 50, wifi_icon_0, 8, 8, SSD1306_WHITE);
      display.drawBitmap(120, 50, x_icon, 8, 8, SSD1306_WHITE);
    }

    display.display();
  }
  #endif
}

// Add missing function declaration
const char* getStatusName(device_status_t status) {
  switch (status) {
    case STATUS_POC: return "POC";
    case STATUS_NEWBORN: return "Newborn";
    case STATUS_TODDLER: return "Toddler";
    case STATUS_CHILD: return "Child";
    case STATUS_TEENAGER: return "Teenager";
    case STATUS_ADULT: return "Adult";
    case STATUS_GEEZER: return "Geezer";
    default: return "Unknown";
  }
}
