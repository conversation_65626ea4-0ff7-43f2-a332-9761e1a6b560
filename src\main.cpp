/*
 * ESP32 Multi-Board Sensor Firmware - Main Application
 * 
 * A modular, multi-board compatible sensor platform for ESP32 devices.
 * Supports Wi-Fi, AWS IoT Core, LoRaWAN (on compatible boards), and various sensors.
 * 
 * Author: Generated for ESP32 sensor project
 * Version: 1.0
 */

#include <Arduino.h>
#include "config.h"

// Core ESP32 libraries
#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <ArduinoJson.h>
#include <Preferences.h>
#include <Wire.h>

// Time libraries
#include <WiFiUdp.h>
#include <NTPClient.h>
#include <time.h>

// Sensor libraries
#include <Adafruit_AM2320.h>

// Display libraries (if available)
#if HAS_OLED
  #include <Adafruit_GFX.h>
  #include <Adafruit_SSD1306.h>
  #include "icons.h"
#endif

// LoRaWAN libraries (if available)
#if HAS_LORA
  // TODO: Add LoRaWAN includes when implementing
  // #include <lmic.h>
  // #include <hal/hal.h>
#endif

// Ethernet libraries (if available)
#if HAS_ETHERNET
  #include <ETH.h>
#endif

// ===== AWS IOT CORE CERTIFICATES =====
// These are the same certificates from the ESP8266 version
// In production, these should be loaded from SPIFFS/LittleFS
static const char certificate_pem_crt[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDWTCCAkGgAwIBAgIUQLLrIO5d6G9m6/tgpMttQs9ncW0wDQYJKoZIhvcNAQEL
BQAwTTFLMEkGA1UECwxCQW1hem9uIFdlYiBTZXJ2aWNlcyBPPUFtYXpvbi5jb20g
SW5jLiBMPVNlYXR0bGUgU1Q9V2FzaGluZ3RvbiBDPVVTMB4XDTI1MDYxMzAwNTgx
NVoXDTQ5MTIzMTIzNTk1OVowHjEcMBoGA1UEAwwTQVdTIElvVCBDZXJ0aWZpY2F0
ZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKsW39KzechA5NkWWfAF
5H6sNeICnc9hjGjZQ3tI6VSHjDFhGNtnf+2eC6HQZ/m86jSOtmJnvYD0lBd/4cle
ZT/LLiDTRgyFL+GKnmgRN0KhNsvgVaR1tnTMY+AszAhpgBf/AHTgoq57d3mG4y22
jXqa8iZmHm4y1tm60/b44gdHbVEdRko1ntCoaOTKqMOJhIWMMO6EjfawcpNwb3Pu
v5PWDGyqWsPEA9ZJ74WZNVAcZkY0ei9jF/lyzJGiNjEKgduQshac4EjZRchFDCZi
5fYmWVyKXn8DZAWxA03ng6pFI/OajcSvSVGa9tTsLb0H1wVvZqX4x7k3u2F3uoe1
Nd0CAwEAAaNgMF4wHwYDVR0jBBgwFoAUqHWRrqPVtVv1vCaOqsZ0Z00nZ24wHQYD
VR0OBBYEFLgIoaMa8XrekF2uZJEgiGTR5NDJMAwGA1UdEwEB/wQCMAAwDgYDVR0P
AQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCg7mMDsgkI33zT4pBQwC8Yyyxs
uwEuMnZu0ekJdWx7UL2aEXc7zyFNDPYEilE3CF7UyQ3D0yn43vIN+jKNSHVtnVWK
Np1B6aT0x2Cd1PDsi0OOoFuhxkQ1evLbfrEh9ECJOYRd3ll2v0yPOpTDAN/nHEOy
tbbRvbt4lwYQagahz+tJcyv0LsZj5rY/m5bDKRTcWj0IdGKF2Y6fc616AvJf/G3s
kHYSGXsOzJJ4gwENzYiGmIS9m6RmSjrwu/zy2dsJ3VHgqmKgeVOaQEkr1Zz4L47m
MlGKkBLCUcmTfc8v2fUf6YVPXwgLjVpzvZ2OU1u5AVMUPat7r16d2Xcy0O76
-----END CERTIFICATE-----
)EOF";

static const char private_pem_key[] PROGMEM = R"EOF(
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)EOF";

static const char amazon_ca_cert[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
ADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6
b24gUm9vdCBDQSAxMB4XDTE1MDUyNjAwMDAwMFoXDTM4MDExNzAwMDAwMFowOTEL
MAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv
b3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj
ca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM
9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw
IFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6
VOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L
93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm
jgSubJrIqg0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0OBBYEFIQYzIU07LwMlJQuCFmcx7IQTgoIMA0GCSqGSIb3DQEBCwUA
A4IBAQCY8jdaQZChGsV2USggNiMOruYou6r4lK5IpDB/G/wkjUu0yKGX9rbxenDI
U5PMCCjjmCXPI6T53iHTfIUJrU6adTrCC2qJeHZERxhlbI1Bjjt/msv0tadQ1wUs
N+gDS63pYaACbvXy8MWy7Vu33PqUXHeeE6V/Uq2V8viTO96LXFvKWlJbYK8U90vv
o/ufQJVtMVT8QtPHRh8jrdkPSHCa2XV4cdFyQzR1bldZwgJcJmApzyMZFo6IQ6XU
5MsI+yMRQ+hDKXJioaldXgjUkK642M4UwtBV8ob2xJNDd2ZhwLnoQdeXeGADbkpy
rqXRfboQnoZsG4q5WTP468SQvvG5
-----END CERTIFICATE-----
)EOF";

// ===== GLOBAL VARIABLES =====

// Device identification
String deviceId;
String clientId;

// Network objects
WiFiUDP ntpUDP;
NTPClient timeClient(ntpUDP, NTP_SERVER, NTP_OFFSET, NTP_UPDATE_INTERVAL);
WiFiClientSecure awsClient;
PubSubClient mqttClient(awsClient);

// Sensor objects
Adafruit_AM2320 am2320;

// Display object (if available)
#if HAS_OLED
  Adafruit_SSD1306 display(OLED_WIDTH, OLED_HEIGHT, &Wire, OLED_RESET);
#endif

// Configuration storage
Preferences preferences;

// System state variables
bool wifiConnected = false;
bool awsConnected = false;
bool ntpSynced = false;
unsigned long lastTelemetryTime = 0;
unsigned long lastSensorReadTime = 0;
unsigned long lastDisplayUpdateTime = 0;
unsigned long lastWifiRetryTime = 0;

// Sensor data structure
struct SensorData {
  float temperature = 0.0;
  float humidity = 0.0;
  float adcVoltage = 0.0;
  uint8_t binaryState = 0;
  unsigned long timestamp = 0;
  String connectionType = "none";
};

SensorData currentSensorData;

// ===== FUNCTION PROTOTYPES =====
void setupSystem();
void setupWiFi();
void setupNTP();
void setupAWS();
void setupSensors();
void setupDisplay();
void readSensors();
void updateDisplay();
void connectAWS();
void publishTelemetry();
void mqttCallback(char* topic, byte* payload, unsigned int length);
void generateDeviceId();

// ===== MAIN FUNCTIONS =====

void setup() {
  Serial.begin(SERIAL_BAUD);
  delay(1000);
  
  DEBUG_PRINTLN("\n===== ESP32 Multi-Board Sensor Firmware =====");
  DEBUG_PRINTF("Board: %s\n", BOARD_NAME);
  DEBUG_PRINTF("Free heap at start: %d bytes\n", ESP.getFreeHeap());
  DEBUG_PRINTF("ESP32 Core version: %s\n", ESP.getSdkVersion());
  
  setupSystem();
  setupSensors();
  setupDisplay();
  setupWiFi();
  
  if (wifiConnected) {
    setupNTP();
    setupAWS();
  }
  
  DEBUG_PRINTLN("Setup complete. Starting main loop...");
}

void loop() {
  unsigned long now = millis();
  
  // Read sensors periodically
  if (now - lastSensorReadTime > SENSOR_READ_INTERVAL) {
    readSensors();
    lastSensorReadTime = now;
  }
  
  // Update display periodically
  #if HAS_OLED
  if (now - lastDisplayUpdateTime > DISPLAY_UPDATE_INTERVAL) {
    updateDisplay();
    lastDisplayUpdateTime = now;
  }
  #endif
  
  // Try to connect to WiFi if not connected
  if (!wifiConnected && (now - lastWifiRetryTime > WIFI_RETRY_INTERVAL)) {
    setupWiFi();
    lastWifiRetryTime = now;
  }
  
  // Try to connect to AWS if WiFi is connected but AWS is not
  if (wifiConnected && !awsConnected) {
    connectAWS();
  }
  
  // Process MQTT messages
  if (awsConnected) {
    mqttClient.loop();
  }
  
  // Publish telemetry periodically
  if (awsConnected && (now - lastTelemetryTime > TELEMETRY_INTERVAL)) {
    publishTelemetry();
    lastTelemetryTime = now;
  }
  
  delay(100); // Small delay to prevent tight loop
}

// ===== IMPLEMENTATION FUNCTIONS =====

void setupSystem() {
  DEBUG_PRINTLN("Initializing system...");

  // Initialize built-in LED
  pinMode(STATUS_LED_PIN, OUTPUT);
  digitalWrite(STATUS_LED_PIN, LOW);

  // Initialize digital inputs with pullups
  pinMode(DIN1_PIN, INPUT_PULLUP);
  pinMode(DIN2_PIN, INPUT_PULLUP);

  // Initialize I2C
  Wire.begin(I2C_SDA, I2C_SCL);

  // Generate unique device ID from MAC address
  generateDeviceId();

  // Initialize preferences
  preferences.begin(CONFIG_NAMESPACE, false);

  DEBUG_PRINTF("Device ID: %s\n", deviceId.c_str());
  DEBUG_PRINTLN("System initialization complete.");
}

void generateDeviceId() {
  // Generate device ID from MAC address
  uint8_t mac[6];
  WiFi.macAddress(mac);
  deviceId = String(mac[0], HEX) + String(mac[1], HEX) + String(mac[2], HEX) +
             String(mac[3], HEX) + String(mac[4], HEX) + String(mac[5], HEX);
  deviceId.toUpperCase();

  // Create client ID for MQTT - use AWS Thing name for compatibility
  clientId = DEFAULT_CLIENT_ID;  // This is now the AWS Thing name
}

void setupWiFi() {
  DEBUG_PRINT("Connecting to WiFi: ");
  DEBUG_PRINTLN(DEFAULT_WIFI_SSID);

  WiFi.mode(WIFI_STA);
  WiFi.begin(DEFAULT_WIFI_SSID, DEFAULT_WIFI_PASS);

  unsigned long startTime = millis();
  while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < WIFI_CONNECT_TIMEOUT) {
    delay(500);
    DEBUG_PRINT(".");
  }

  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    DEBUG_PRINTLN("\nWiFi connected!");
    DEBUG_PRINTF("IP: %s\n", WiFi.localIP().toString().c_str());
    DEBUG_PRINTF("RSSI: %d dBm\n", WiFi.RSSI());
    digitalWrite(STATUS_LED_PIN, HIGH); // Turn on status LED
  } else {
    wifiConnected = false;
    DEBUG_PRINTLN("\nWiFi connection failed!");
    digitalWrite(STATUS_LED_PIN, LOW); // Turn off status LED
  }
}

void setupNTP() {
  if (!wifiConnected) return;

  DEBUG_PRINTLN("Starting NTP client...");
  timeClient.begin();

  int attempts = 0;
  while (!timeClient.update() && attempts < 10) {
    delay(1000);
    attempts++;
    DEBUG_PRINT(".");
  }

  if (timeClient.isTimeSet()) {
    ntpSynced = true;
    time_t epochTime = timeClient.getEpochTime();
    DEBUG_PRINTLN("\nNTP time synchronized!");
    DEBUG_PRINTF("Current time: %s\n", timeClient.getFormattedTime().c_str());
    DEBUG_PRINTF("Epoch time: %lu\n", epochTime);
  } else {
    ntpSynced = false;
    DEBUG_PRINTLN("\nNTP sync failed!");
  }
}

void setupAWS() {
  if (!wifiConnected) return;

  DEBUG_PRINTLN("Setting up AWS IoT...");
  DEBUG_PRINTF("Free heap before AWS setup: %d bytes\n", ESP.getFreeHeap());

  // Load certificates
  awsClient.setCACert(amazon_ca_cert);
  awsClient.setCertificate(certificate_pem_crt);
  awsClient.setPrivateKey(private_pem_key);

  // Configure MQTT client
  mqttClient.setServer(AWS_IOT_ENDPOINT, AWS_IOT_PORT);
  mqttClient.setCallback(mqttCallback);
  mqttClient.setBufferSize(MQTT_BUFFER_SIZE);

  DEBUG_PRINTF("Free heap after AWS setup: %d bytes\n", ESP.getFreeHeap());
  DEBUG_PRINTLN("AWS IoT setup complete.");
}

void setupSensors() {
  DEBUG_PRINTLN("Initializing sensors...");

  // Initialize AM2320 sensor
  if (am2320.begin()) {
    DEBUG_PRINTLN("AM2320 sensor initialized successfully.");
  } else {
    DEBUG_PRINTLN("Failed to initialize AM2320 sensor!");
  }

  DEBUG_PRINTLN("Sensor initialization complete.");
}

void setupDisplay() {
  #if HAS_OLED
  DEBUG_PRINTLN("Initializing OLED display...");

  if (display.begin(SSD1306_SWITCHCAPVCC, OLED_ADDRESS)) {
    DEBUG_PRINTLN("OLED display initialized successfully.");

    // Clear display and show startup message
    display.clearDisplay();
    display.setTextSize(1);
    display.setTextColor(SSD1306_WHITE);
    display.setCursor(0, 0);
    display.println("ESP32 Sensor");
    display.println(BOARD_NAME);
    display.printf("ID: %s", deviceId.substring(0, 8).c_str());
    display.display();

  } else {
    DEBUG_PRINTLN("Failed to initialize OLED display!");
  }
  #else
  DEBUG_PRINTLN("No OLED display available on this board.");
  #endif
}

void readSensors() {
  // Read AM2320 temperature and humidity
  float temp = am2320.readTemperature();
  float hum = am2320.readHumidity();

  if (!isnan(temp) && !isnan(hum)) {
    currentSensorData.temperature = temp;
    currentSensorData.humidity = hum;
  }

  // Read ADC voltage
  int adcRaw = analogRead(ADC_PIN);
  currentSensorData.adcVoltage = (adcRaw / 4095.0) * 3.3; // ESP32 ADC reference is 3.3V

  // Read digital inputs
  bool din1 = !digitalRead(DIN1_PIN); // Inverted because of pullup
  bool din2 = !digitalRead(DIN2_PIN); // Inverted because of pullup
  currentSensorData.binaryState = (din2 << 1) | din1; // 2-bit value

  // Set timestamp
  currentSensorData.timestamp = timeClient.getEpochTime();

  // Set connection type
  if (awsConnected) {
    currentSensorData.connectionType = "WiFi-AWS";
  } else if (wifiConnected) {
    currentSensorData.connectionType = "WiFi";
  } else {
    currentSensorData.connectionType = "none";
  }
}

void updateDisplay() {
  #if HAS_OLED
  display.clearDisplay();
  display.setTextSize(1);
  display.setTextColor(SSD1306_WHITE);

  // Line 1: Board name and device ID
  display.setCursor(0, 0);
  display.printf("%s", BOARD_NAME);

  // Line 2: Temperature and Humidity
  display.setCursor(0, 10);
  display.printf("Temp: %.1f C", currentSensorData.temperature);

  display.setCursor(0, 20);
  display.printf("Hum:  %.1f %%", currentSensorData.humidity);

  // Line 3: ADC and Digital inputs
  display.setCursor(0, 30);
  display.printf("ADC0: %.2f V", currentSensorData.adcVoltage);

  display.setCursor(0, 40);
  display.printf("Din1:%d Din2:%d",
                 currentSensorData.binaryState & 1,
                 (currentSensorData.binaryState >> 1) & 1);

  // Status line: Connection status
  display.setCursor(0, 50);
  if (awsConnected) {
    display.print("AWS: Connected");
    // Draw WiFi icon
    display.drawBitmap(110, 50, wifi_icon_6, 8, 8, SSD1306_WHITE);
    display.drawBitmap(120, 50, checkmark_icon, 8, 8, SSD1306_WHITE);
  } else if (wifiConnected) {
    display.print("WiFi: Connected");
    display.drawBitmap(110, 50, wifi_icon_4, 8, 8, SSD1306_WHITE);
    display.drawBitmap(120, 50, x_icon, 8, 8, SSD1306_WHITE);
  } else {
    display.print("No Connection");
    display.drawBitmap(110, 50, wifi_icon_0, 8, 8, SSD1306_WHITE);
    display.drawBitmap(120, 50, x_icon, 8, 8, SSD1306_WHITE);
  }

  display.display();
  #endif
}

void connectAWS() {
  if (!wifiConnected || awsConnected) return;

  DEBUG_PRINTF("Attempting AWS connection... Heap: %d bytes\n", ESP.getFreeHeap());

  // Try to connect with client ID
  if (mqttClient.connect(clientId.c_str())) {
    awsConnected = true;
    DEBUG_PRINTLN("AWS IoT connected!");

    // Subscribe to config topic - using AWS compatible topic
    mqttClient.subscribe(CONFIG_TOPIC);
    DEBUG_PRINTF("Subscribed to: %s\n", CONFIG_TOPIC);

  } else {
    awsConnected = false;
    DEBUG_PRINTF("AWS connection failed. State: %d\n", mqttClient.state());
  }
}

void publishTelemetry() {
  if (!awsConnected) return;

  DEBUG_PRINTLN("Publishing telemetry...");

  // Create JSON payload
  StaticJsonDocument<JSON_BUFFER_SIZE> doc;
  doc["device_id"] = deviceId;
  doc["timestamp"] = currentSensorData.timestamp;
  doc["temperature"] = currentSensorData.temperature;
  doc["humidity"] = currentSensorData.humidity;
  doc["adc_voltage"] = currentSensorData.adcVoltage;
  doc["binary_state"] = currentSensorData.binaryState;
  doc["connection"] = currentSensorData.connectionType;
  doc["rssi"] = WiFi.RSSI();
  doc["heap"] = ESP.getFreeHeap();
  doc["uptime"] = millis() / 1000;

  String payload;
  serializeJson(doc, payload);

  // Publish to telemetry topic
  String telemetryTopic = TELEMETRY_TOPIC_PREFIX + deviceId + TELEMETRY_TOPIC_SUFFIX;

  if (mqttClient.publish(telemetryTopic.c_str(), payload.c_str())) {
    DEBUG_PRINTF("Telemetry published to: %s\n", telemetryTopic.c_str());
    DEBUG_PRINTF("Payload: %s\n", payload.c_str());
  } else {
    DEBUG_PRINTLN("Failed to publish telemetry!");
  }
}

void mqttCallback(char* topic, byte* payload, unsigned int length) {
  DEBUG_PRINTF("Message received [%s]: ", topic);

  // Convert payload to string
  String message;
  for (unsigned int i = 0; i < length; i++) {
    message += (char)payload[i];
  }
  DEBUG_PRINTLN(message);

  // TODO: Parse configuration messages and update settings
  // This would handle commands like changing sensor offsets, units, etc.
}
