/*
 * ESP32 Multi-Board Sensor Configuration
 * 
 * This file contains all board-specific pin definitions, default configurations,
 * and compile-time settings for the multi-board ESP32 sensor firmware.
 * 
 * Supported boards:
 * - BOARD_ESP32_GENERIC: Standard 30-pin ESP32 dev board
 * - BOARD_WEMOS_D32: Wemos D32 board
 * - BOARD_HELTEC_LORA: Heltec WiFi LoRa ESP32 (with LoRaWAN support)
 * - BOARD_WT32_ETH01: WT32-ETH01 with Ethernet support
 */

#pragma once
#include <Arduino.h>

// ===== DEBUG CONFIGURATION =====
#ifdef DEBUG
  #define DEBUG_PRINT(x) Serial.print(x)
  #define DEBUG_PRINTLN(x) Serial.println(x)
  #define DEBUG_PRINTF(x, ...) Serial.printf(x, __VA_ARGS__)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
  #define DEBUG_PRINTF(x, ...)
#endif

// ===== BOARD-<PERSON>ECIF<PERSON> PIN DEFINITIONS =====

#ifdef BOARD_ESP32_GENERIC
  // Standard ESP32 30-pin dev board
  #define BOARD_NAME "ESP32-Generic"
  #define HAS_OLED true
  #define HAS_LORA false
  #define HAS_ETHERNET false
  
  // I2C pins for OLED and AM2320
  #define I2C_SDA 21
  #define I2C_SCL 22
  
  // Digital inputs with pullups
  #define DIN1_PIN 18
  #define DIN2_PIN 19
  
  // ADC input
  #define ADC_PIN A6  // GPIO34
  
  // OLED display (128x64, I2C)
  #define OLED_WIDTH 128
  #define OLED_HEIGHT 64
  #define OLED_RESET -1
  #define OLED_ADDRESS 0x3C

#elif defined(BOARD_WEMOS_D32)
  // Wemos D32 board
  #define BOARD_NAME "Wemos-D32"
  #define HAS_OLED true
  #define HAS_LORA false
  #define HAS_ETHERNET false
  
  // I2C pins
  #define I2C_SDA 21
  #define I2C_SCL 22
  
  // Digital inputs
  #define DIN1_PIN 18
  #define DIN2_PIN 19
  
  // ADC input
  #define ADC_PIN A0
  
  // OLED display
  #define OLED_WIDTH 128
  #define OLED_HEIGHT 64
  #define OLED_RESET -1
  #define OLED_ADDRESS 0x3C

#elif defined(BOARD_HELTEC_LORA)
  // Heltec WiFi LoRa ESP32
  #define BOARD_NAME "Heltec-LoRa"
  #define HAS_OLED true
  #define HAS_LORA true
  #define HAS_ETHERNET false
  
  // I2C pins (Heltec specific)
  #define I2C_SDA 4
  #define I2C_SCL 15
  
  // Digital inputs
  #define DIN1_PIN 12
  #define DIN2_PIN 13
  
  // ADC input
  #define ADC_PIN A0
  
  // OLED display (built-in)
  #define OLED_WIDTH 128
  #define OLED_HEIGHT 64
  #define OLED_RESET 16
  #define OLED_ADDRESS 0x3C
  
  // LoRa pins (Heltec specific)
  #define LORA_SCK 5
  #define LORA_MISO 19
  #define LORA_MOSI 27
  #define LORA_SS 18
  #define LORA_RST 14
  #define LORA_DIO0 26
  #define LORA_DIO1 33
  #define LORA_DIO2 32

#elif defined(BOARD_WT32_ETH01)
  // WT32-ETH01 with Ethernet
  #define BOARD_NAME "WT32-ETH01"
  #define HAS_OLED false  // No built-in OLED
  #define HAS_LORA false
  #define HAS_ETHERNET true
  
  // I2C pins (if external OLED connected)
  #define I2C_SDA 21
  #define I2C_SCL 22
  
  // Digital inputs
  #define DIN1_PIN 32
  #define DIN2_PIN 33
  
  // ADC input
  #define ADC_PIN A0
  
  // Ethernet pins (built-in)
  #define ETH_PHY_POWER 16
  #define ETH_PHY_MDC 23
  #define ETH_PHY_MDIO 18

#else
  #error "No board type defined! Please define one of: BOARD_ESP32_GENERIC, BOARD_WEMOS_D32, BOARD_HELTEC_LORA, BOARD_WT32_ETH01"
#endif

// ===== DEFAULT CONFIGURATION VALUES =====

// WiFi Configuration
#define DEFAULT_WIFI_SSID "HaciendaHagansLR"
#define DEFAULT_WIFI_PASS "F1nglongers"
#define WIFI_CONNECT_TIMEOUT 30000  // 30 seconds
#define WIFI_RETRY_INTERVAL 60000   // 1 minute

// AWS IoT Configuration
#define AWS_IOT_ENDPOINT "a2ltfva6jq44zv-ats.iot.us-east-1.amazonaws.com"
#define AWS_IOT_PORT 8883
#define DEFAULT_CLIENT_ID "esp32_sensor_device"

// MQTT Topics
#define TELEMETRY_TOPIC_PREFIX "/devices/"
#define TELEMETRY_TOPIC_SUFFIX "/telemetry"
#define CONFIG_TOPIC_PREFIX "/devices/"
#define CONFIG_TOPIC_SUFFIX "/config"

// Sensor Configuration
#define TELEMETRY_INTERVAL 60000    // 60 seconds
#define SENSOR_READ_INTERVAL 5000   // 5 seconds
#define DEFAULT_TEMP_UNIT 'C'       // 'C' or 'F'
#define DEFAULT_TEMP_OFFSET 0       // Signed integer offset
#define DEFAULT_HUM_OFFSET 0        // Signed integer offset
#define DEFAULT_ADC_OFFSET 0        // Signed integer offset
#define DEFAULT_ADC_MULTIPLIER 1.0  // Float multiplier
#define DEFAULT_ADC_LABEL "ADC0"
#define DEFAULT_SENSOR_NAME "ESP32-Sensor"

// Digital Input Configuration
#define DIN1_LABEL "Din1"
#define DIN2_LABEL "Din2"
#define DIGITAL_INPUT_PULLUP true

// NTP Configuration
#define NTP_SERVER "pool.ntp.org"
#define NTP_OFFSET 0                // UTC offset in seconds
#define NTP_UPDATE_INTERVAL 60000   // 1 minute

// Display Configuration
#define DISPLAY_UPDATE_INTERVAL 2000  // 2 seconds
#define DISPLAY_TIMEOUT 300000        // 5 minutes (auto-off)

// LoRaWAN Configuration (for Heltec boards)
#if HAS_LORA
  #define LORAWAN_REGION US915
  #define LORAWAN_CLASS CLASS_A
  #define LORAWAN_ADR true
  #define LORAWAN_DATARATE DR_3
  #define LORAWAN_TX_POWER 14
  #define LORAWAN_UPLINK_INTERVAL 300000  // 5 minutes
#endif

// EEPROM/Preferences Configuration
#define CONFIG_NAMESPACE "sensor_cfg"
#define CONFIG_VERSION 1

// System Configuration
#define SERIAL_BAUD 115200
#define WATCHDOG_TIMEOUT 30000      // 30 seconds
#define STATUS_LED_PIN 2            // Built-in LED on most ESP32 boards

// Memory and Performance
#define JSON_BUFFER_SIZE 512
#define MQTT_BUFFER_SIZE 512
#define MAX_WIFI_NETWORKS 20

// Error Handling
#define MAX_RETRY_ATTEMPTS 3
#define ERROR_RESET_THRESHOLD 10    // Reset after 10 consecutive errors
