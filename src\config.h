/*
 * ESP32 Multi-Board Sensor Configuration
 * 
 * This file contains all board-specific pin definitions, default configurations,
 * and compile-time settings for the multi-board ESP32 sensor firmware.
 * 
 * Supported boards:
 * - BOARD_ESP32_GENERIC: Standard 30-pin ESP32 dev board
 * - BOARD_WEMOS_D32: Wemos D32 board
 * - BOARD_HELTEC_LORA: Heltec WiFi LoRa ESP32 (with LoRaWAN support)
 * - BOARD_WT32_ETH01: WT32-ETH01 with Ethernet support
 */

#pragma once
#include <Arduino.h>

// ===== INITIAL OPERATING MODE CONFIGURATION =====
// Uncomment ONE of the following modes:
#define INITIAL_MODE_POC        // POC: Use default AWS credentials, adult status
// #define INITIAL_MODE_NEWBORN    // Newborn: No config, needs provisioning
// #define INITIAL_MODE_PREPROV    // Pre-provisioned: Factory configured

// Default to newborn if no mode specified
#if !defined(INITIAL_MODE_POC) && !defined(INITIAL_MODE_NEWBORN) && !defined(INITIAL_MODE_PREPROV)
  #define INITIAL_MODE_NEWBORN
#endif

// ===== DEVICE LIFECYCLE STATUS CODES =====
typedef enum {
  STATUS_POC = 0,        // POC mode - using default credentials
  STATUS_NEWBORN = 1,    // No configuration, needs provisioning
  STATUS_TODDLER = 2,    // Partial configuration
  STATUS_CHILD = 3,      // Fully configured but something wrong
  STATUS_TEENAGER = 4,   // Fully configured, verification needed
  STATUS_ADULT = 5,      // Operational mode
  STATUS_GEEZER = 6      // Credentials expired, needs renewal
} device_status_t;

// ===== DEBUG CONFIGURATION =====
#ifdef DEBUG
  #define DEBUG_PRINT(x) Serial.print(x)
  #define DEBUG_PRINTLN(x) Serial.println(x)
  #define DEBUG_PRINTF(x, ...) Serial.printf(x, __VA_ARGS__)
#else
  #define DEBUG_PRINT(x)
  #define DEBUG_PRINTLN(x)
  #define DEBUG_PRINTF(x, ...)
#endif

// ===== BOARD-SPECIFIC PIN DEFINITIONS =====

#ifdef BOARD_ESP32_GENERIC
  // Standard ESP32 30-pin dev board
  #define BOARD_NAME "ESP32-Generic"
  #define HAS_OLED true
  #define HAS_LORA false
  #define HAS_ETHERNET false
  
  // I2C pins for OLED and AM2320
  #define I2C_SDA 21
  #define I2C_SCL 22
  
  // Digital inputs with pullups
  #define DIN1_PIN 18
  #define DIN2_PIN 19
  
  // ADC input
  #define ADC_PIN A6  // GPIO34
  
  // OLED display (128x64, I2C)
  #define OLED_WIDTH 128
  #define OLED_HEIGHT 64
  #define OLED_RESET -1
  #define OLED_ADDRESS 0x3C

#elif defined(BOARD_WEMOS_D32)
  // Wemos D32 board
  #define BOARD_NAME "Wemos-D32"
  #define HAS_OLED true
  #define HAS_LORA false
  #define HAS_ETHERNET false
  
  // I2C pins
  #define I2C_SDA 21
  #define I2C_SCL 22
  
  // Digital inputs
  #define DIN1_PIN 18
  #define DIN2_PIN 19
  
  // ADC input
  #define ADC_PIN A0
  
  // OLED display
  #define OLED_WIDTH 128
  #define OLED_HEIGHT 64
  #define OLED_RESET -1
  #define OLED_ADDRESS 0x3C

#elif defined(BOARD_HELTEC_LORA)
  // Heltec WiFi LoRa ESP32
  #define BOARD_NAME "Heltec-LoRa"
  #define HAS_OLED true
  #define HAS_LORA true
  #define HAS_ETHERNET false
  
  // I2C pins (Heltec specific)
  #define I2C_SDA 4
  #define I2C_SCL 15
  
  // Digital inputs
  #define DIN1_PIN 12
  #define DIN2_PIN 13
  
  // ADC input
  #define ADC_PIN A0
  
  // OLED display (built-in)
  #define OLED_WIDTH 128
  #define OLED_HEIGHT 64
  #define OLED_RESET 16
  #define OLED_ADDRESS 0x3C
  
  // LoRa pins (Heltec specific)
  #define LORA_SCK 5
  #define LORA_MISO 19
  #define LORA_MOSI 27
  #define LORA_SS 18
  #define LORA_RST 14
  #define LORA_DIO0 26
  #define LORA_DIO1 33
  #define LORA_DIO2 32

#elif defined(BOARD_WT32_ETH01)
  // WT32-ETH01 with Ethernet
  #define BOARD_NAME "WT32-ETH01"
  #define HAS_OLED false  // No built-in OLED
  #define HAS_LORA false
  #define HAS_ETHERNET true
  
  // I2C pins (if external OLED connected)
  #define I2C_SDA 21
  #define I2C_SCL 22
  
  // Digital inputs
  #define DIN1_PIN 32
  #define DIN2_PIN 33
  
  // ADC input
  #define ADC_PIN A0
  
  // Ethernet pins (built-in)
  #define ETH_PHY_POWER 16
  #define ETH_PHY_MDC 23
  #define ETH_PHY_MDIO 18

#else
  #error "No board type defined! Please define one of: BOARD_ESP32_GENERIC, BOARD_WEMOS_D32, BOARD_HELTEC_LORA, BOARD_WT32_ETH01"
#endif

// ===== DEFAULT CONFIGURATION VALUES =====

// ===== DEVICE IDENTIFICATION =====
#define DEVICE_TYPE "ESP32-Sensor"
#define FIRMWARE_VERSION "1.0.0"
#define HARDWARE_VERSION "1.0"

// ===== BASE62 ENCODING FOR DEVICE ID =====
const char base62_chars[] = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";

// ===== PROVISIONING CONFIGURATION =====
#define PROVISIONING_ENDPOINT "your-provisioning-broker.com"
#define PROVISIONING_PORT 1883
#define PROVISIONING_USERNAME "factory_device"
#define PROVISIONING_PASSWORD "factory_password"
#define PROVISIONING_RETRY_INTERVAL 3600000  // 1 hour
#define PROVISIONING_JITTER_MAX 600000       // 10 minutes max jitter

// ===== WIFI CONFIGURATION =====
#define DEFAULT_WIFI_SSID "HaciendaHagansLR"
#define DEFAULT_WIFI_PASS "F1nglongers"
#define WIFI_CONNECT_TIMEOUT 30000  // 30 seconds
#define WIFI_RETRY_INTERVAL 60000   // 1 minute

// AWS IoT Configuration
#define AWS_IOT_ENDPOINT "a2ltfva6jq44zv-ats.iot.us-east-1.amazonaws.com"
#define AWS_IOT_PORT 8883
#define AWS_THING_NAME "pi_aggregator_adcbc1"
#define DEFAULT_CLIENT_ID AWS_THING_NAME  // Use Thing name as client ID

// ===== MQTT TOPICS =====
// Provisioning topics (for newborn devices)
#define HELLO_TOPIC_PREFIX "device/"
#define CONFIG_TOPIC_PREFIX "config/"
#define COMMAND_TOPIC_PREFIX "command/"
#define COMMAND_ACK_TOPIC_PREFIX "command_ack/"

// Operational topics (POC mode and adult devices)
#define POC_TELEMETRY_PREFIX "sensors/pi_aggregator_adcbc1/"
#define POC_CONFIG_PREFIX "sensors/pi_aggregator_adcbc1/"
#define POC_COMMAND_PREFIX "sensors/pi_aggregator_adcbc1/"

// Topic suffixes
#define TELEMETRY_SUFFIX "/telemetry"
#define CONFIG_SUFFIX "/config"
#define COMMAND_SUFFIX "/command"
#define STATUS_SUFFIX "/status"

// ===== DEVICE CONFIGURATION STRUCTURE =====
typedef struct {
  // EEPROM version and validation
  uint16_t config_version;
  uint32_t config_crc;

  // Device status and identification
  device_status_t status_code;
  char device_id[8];           // 6-char base62 + null terminator
  char device_type[32];
  char firmware_version[16];
  char hardware_version[16];
  char ui_name[64];

  // Network configuration
  char wifi_ssid[64];
  char wifi_pass[64];
  bool use_static_ip;
  uint32_t static_ip;
  uint32_t static_gateway;
  uint32_t static_subnet;
  uint32_t static_dns1;
  uint32_t static_dns2;

  // MQTT configuration
  char mqtt_endpoint[128];
  uint16_t mqtt_port;
  char mqtt_client_id[64];
  char mqtt_username[64];
  char mqtt_password[64];

  // AWS IoT certificates (stored as strings)
  char mqtt_cert[2048];
  char mqtt_key[2048];
  char mqtt_ca[2048];

  // Publishing configuration
  uint32_t publish_interval_sec;
  bool publish_regardless_of_change;
  bool stay_connected;

  // Sensor calibration
  float adc_offset;
  float adc_multiplier;
  bool adc_offset_first;       // Order: (raw+offset)*mult or (raw*mult)+offset
  uint8_t adc_decimal_places;
  char adc_label[32];

  float temp_offset;
  float temp_multiplier;
  bool temp_offset_first;
  uint8_t temp_decimal_places;
  char temp_unit;              // 'C' or 'F'

  float hum_offset;
  float hum_multiplier;
  bool hum_offset_first;
  uint8_t hum_decimal_places;

  // Binary sensor configuration
  bool bin1_invert;
  char bin1_label_on[32];
  char bin1_label_off[32];

  bool bin2_invert;
  char bin2_label_on[32];
  char bin2_label_off[32];

  // Display configuration
  uint16_t sensor_read_interval_ms;
  uint16_t display_refresh_interval_ms;

  // Alert thresholds
  float temp_alert_min;
  float temp_alert_max;
  float hum_alert_min;
  float hum_alert_max;
  float adc_alert_min;
  float adc_alert_max;
  float alert_hysteresis;

  // Retry and timing configuration
  uint32_t connection_retry_interval_ms;
  uint8_t max_connection_failures;

} device_config_t;

// ===== DEFAULT CONFIGURATION VALUES =====
#define CONFIG_VERSION 1
#define DEFAULT_PUBLISH_INTERVAL 60
#define DEFAULT_SENSOR_READ_INTERVAL 5000
#define DEFAULT_DISPLAY_REFRESH_INTERVAL 2000
#define DEFAULT_TEMP_UNIT 'C'
#define DEFAULT_TEMP_OFFSET 0.0f
#define DEFAULT_HUM_OFFSET 0.0f
#define DEFAULT_ADC_OFFSET 0.0f
#define DEFAULT_ADC_MULTIPLIER 1.0f
#define DEFAULT_ADC_LABEL "ADC0"
#define DEFAULT_SENSOR_NAME "ESP32-Sensor"
#define DEFAULT_BIN1_LABEL_ON "Din1"
#define DEFAULT_BIN1_LABEL_OFF "Din1"
#define DEFAULT_BIN2_LABEL_ON "Din2"
#define DEFAULT_BIN2_LABEL_OFF "Din2"
#define DEFAULT_DECIMAL_PLACES 1
#define DEFAULT_MAX_FAILURES 3

// Digital Input Configuration
#define DIN1_LABEL "Din1"
#define DIN2_LABEL "Din2"
#define DIGITAL_INPUT_PULLUP true

// NTP Configuration
#define NTP_SERVER "pool.ntp.org"
#define NTP_OFFSET 0                // UTC offset in seconds
#define NTP_UPDATE_INTERVAL 60000   // 1 minute

// Display Configuration
#define DISPLAY_TIMEOUT 300000        // 5 minutes (auto-off)

// LoRaWAN Configuration (for Heltec boards)
#if HAS_LORA
  #define LORAWAN_REGION US915
  #define LORAWAN_CLASS CLASS_A
  #define LORAWAN_ADR true
  #define LORAWAN_DATARATE DR_3
  #define LORAWAN_TX_POWER 14
  #define LORAWAN_UPLINK_INTERVAL 300000  // 5 minutes
#endif

// ===== EEPROM/PREFERENCES CONFIGURATION =====
#define CONFIG_NAMESPACE "sensor_cfg"
#define EEPROM_SIZE 4096

// ===== TIMING CONSTANTS (for backward compatibility) =====
#define SENSOR_READ_INTERVAL (config.sensor_read_interval_ms)
#define TELEMETRY_INTERVAL (config.publish_interval_sec * 1000)
#define DISPLAY_UPDATE_INTERVAL (config.display_refresh_interval_ms)

// System Configuration
#define SERIAL_BAUD 115200
#define WATCHDOG_TIMEOUT 30000      // 30 seconds
#define STATUS_LED_PIN 2            // Built-in LED on most ESP32 boards

// Memory and Performance
#define JSON_BUFFER_SIZE 512
#define MQTT_BUFFER_SIZE 512
#define MAX_WIFI_NETWORKS 20

// Error Handling
#define MAX_RETRY_ATTEMPTS 3
#define ERROR_RESET_THRESHOLD 10    // Reset after 10 consecutive errors
