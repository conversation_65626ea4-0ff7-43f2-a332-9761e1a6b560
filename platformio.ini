; PlatformIO Project Configuration File for AWS IoT Sensor
;
; ESP8266 NodeMCU v1 with AM2320 sensor, OLED display, and AWS IoT Core connectivity
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:nodemcu]
platform = espressif8266
platform_packages = 
  framework-arduinoespressif8266 @ https://github.com/esp8266/Arduino.git#3.1.2
board = nodemcu
framework = arduino

monitor_port = COM4
monitor_speed = 115200
upload_port = COM4
upload_speed = 115200
upload_resetmethod = nodemcu

build_flags =
  -DDEBUG_ESP_PORT=Serial
  -DDEBUG_BEARSSL

lib_deps =
  https://github.com/Imroy/pubsubclient.git#master
  adafruit/Adafruit Unified Sensor@^1.1.9
  adafruit/Adafruit AM2320 sensor library@^1.2.5
  adafruit/Adafruit SSD1306@^2.5.7
  adafruit/Adafruit GFX Library@^1.11.9
  bblanchon/Arduino<PERSON>son@^6.21.2
  paulstoffregen/Time@^1.6.1
  arduino-libraries/NTPClient@^3.2.1

board_build.flash_mode = dio

; ===== ESP32 ENVIRONMENT =====
[env:esp32dev]
platform = espressif32
board = esp32dev
framework = arduino

; Build flags for board selection - uncomment ONE
build_flags =
    -D BOARD_ESP32_GENERIC
    ; -D BOARD_WEMOS_D32
    ; -D BOARD_HELTEC_LORA
    ; -D BOARD_WT32_ETH01
    -D DEBUG=1

; Serial monitor settings
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; Library dependencies for ESP32
lib_deps =
    ; Core ESP32 libraries (built-in)
    WiFi
    WiFiClientSecure
    EEPROM
    Wire
    SPI

    ; AWS IoT and MQTT
    knolleary/PubSubClient@^2.8
    bblanchon/ArduinoJson@^6.21.0

    ; Sensors
    adafruit/Adafruit AM2320 sensor library@^1.2.0
    adafruit/Adafruit Unified Sensor@^1.1.0

    ; Display
    adafruit/Adafruit SSD1306@^2.5.0
    adafruit/Adafruit GFX Library@^1.11.0

    ; Time synchronization
    arduino-libraries/NTPClient@^3.2.0

    ; Configuration storage
    Preferences

; Upload settings
upload_speed = 921600

; Filesystem for certificates
board_build.filesystem = littlefs
