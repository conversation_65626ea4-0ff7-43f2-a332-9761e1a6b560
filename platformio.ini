; PlatformIO Project Configuration File for AWS IoT Sensor
;
; ESP8266 NodeMCU v1 with AM2320 sensor, OLED display, and AWS IoT Core connectivity
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:nodemcu]
platform = espressif8266
platform_packages = 
  framework-arduinoespressif8266 @ https://github.com/esp8266/Arduino.git#3.1.2
board = nodemcu
framework = arduino

monitor_port = COM4
monitor_speed = 115200
upload_port = COM4
upload_speed = 115200
upload_resetmethod = nodemcu

build_flags =
  -DDEBUG_ESP_PORT=Serial
  -DDEBUG_BEARSSL

lib_deps =
  https://github.com/Imroy/pubsubclient.git#master
  adafruit/Adafruit Unified Sensor@^1.1.9
  adafruit/Adafruit AM2320 sensor library@^1.2.5
  adafruit/Adafruit SSD1306@^2.5.7
  adafruit/Adafruit GFX Library@^1.11.9
  bblanchon/Arduino<PERSON><PERSON>@^6.21.2
  paulstoffregen/Time@^1.6.1
  arduino-libraries/NTPClient@^3.2.1

board_build.flash_mode = dio
