/*
 * Provisioning Handler
 * 
 * Handles device provisioning including:
 * - Hello message publishing
 * - Configuration message parsing and validation
 * - Dynamic configuration updates
 */

#include <Arduino.h>
#include "config.h"
#include <ArduinoJson.h>
#include <WiFi.h>
#include <WiFiUdp.h>
#include <NTPClient.h>

// External references
extern device_config_t config;
extern String deviceId;
extern String macAddress;
extern NTPClient timeClient;

// Function prototypes
void saveConfigurationToEEPROM();
void handleConfigurationReceived();
bool publishMessage(const char* topic, const char* payload, bool retain = false);
String getTelemetryTopic();
bool isOperationalStatus();
bool isProvisioningStatus();

// Sensor data structure (should match main.cpp)
struct SensorData {
  float temperature = 0.0;
  float humidity = 0.0;
  float adcVoltage = 0.0;
  uint8_t binaryState = 0;
  unsigned long timestamp = 0;
  String connectionType = "none";
};

extern SensorData currentSensorData;

bool publishHelloMessage() {
  if (!isProvisioningStatus() && config.status_code != STATUS_POC) {
    return false; // Only publish hello in provisioning modes or POC
  }
  
  DEBUG_PRINTLN("Publishing hello message...");
  
  // Create hello JSON
  StaticJsonDocument<JSON_BUFFER_SIZE> doc;
  
  // Required fields
  doc["deviceID"] = deviceId;
  doc["statusCode"] = config.status_code;
  doc["deviceType"] = config.device_type;
  doc["firmwareVersion"] = config.firmware_version;
  doc["hardwareVersion"] = config.hardware_version;
  doc["macAddress"] = macAddress;
  doc["timestamp"] = timeClient.getEpochTime();
  
  // Additional metadata
  doc["rssi"] = WiFi.RSSI();
  doc["heap"] = ESP.getFreeHeap();
  doc["uptime"] = millis() / 1000;
  doc["ip"] = WiFi.localIP().toString();
  
  // Status-specific notes
  switch (config.status_code) {
    case STATUS_POC:
      doc["notes"] = "POC mode - using default credentials";
      break;
    case STATUS_NEWBORN:
      doc["notes"] = "New device requesting initial provisioning";
      break;
    case STATUS_TODDLER:
      doc["notes"] = "Partial configuration received, awaiting completion";
      break;
    case STATUS_CHILD:
      doc["notes"] = "Configuration issues detected, needs attention";
      break;
    case STATUS_TEENAGER:
      doc["notes"] = "Configuration complete, requesting verification";
      break;
    case STATUS_GEEZER:
      doc["notes"] = "Credentials expired, requesting renewal";
      break;
    default:
      doc["notes"] = "Status update";
      break;
  }
  
  // Current configuration summary (for debugging)
  JsonObject configSummary = doc.createNestedObject("config");
  configSummary["wifi_ssid"] = config.wifi_ssid;
  configSummary["mqtt_endpoint"] = config.mqtt_endpoint;
  configSummary["mqtt_port"] = config.mqtt_port;
  configSummary["publish_interval"] = config.publish_interval_sec;
  configSummary["ui_name"] = config.ui_name;
  
  String payload;
  serializeJson(doc, payload);
  
  // Determine topic
  String topic;
  if (config.status_code == STATUS_POC || isOperationalStatus()) {
    topic = getTelemetryTopic(); // Use telemetry topic for POC/operational
  } else {
    topic = String(HELLO_TOPIC_PREFIX) + deviceId; // Use hello topic for provisioning
  }
  
  return publishMessage(topic.c_str(), payload.c_str());
}

bool publishTelemetryMessage() {
  if (!isOperationalStatus() && config.status_code != STATUS_POC) {
    return false; // Only publish telemetry in operational modes
  }
  
  DEBUG_PRINTLN("Publishing telemetry message...");
  
  // Create telemetry JSON
  StaticJsonDocument<JSON_BUFFER_SIZE> doc;
  
  // Device identification
  doc["device_id"] = deviceId;
  doc["timestamp"] = currentSensorData.timestamp;
  doc["status"] = config.status_code;
  
  // Sensor data (limited to 1 decimal place for realistic precision)
  doc["temperature"] = round(currentSensorData.temperature * 10.0) / 10.0;
  doc["humidity"] = round(currentSensorData.humidity * 10.0) / 10.0;
  doc["adc_voltage"] = round(currentSensorData.adcVoltage * 10.0) / 10.0;
  doc["binary_state"] = currentSensorData.binaryState;
  
  // System information
  doc["connection"] = currentSensorData.connectionType;
  doc["rssi"] = WiFi.RSSI();
  doc["heap"] = ESP.getFreeHeap();
  doc["uptime"] = millis() / 1000;
  
  // Configuration version for tracking
  doc["config_version"] = config.config_version;
  doc["config_crc"] = config.config_crc;
  
  String payload;
  serializeJson(doc, payload);
  
  String topic = getTelemetryTopic();
  return publishMessage(topic.c_str(), payload.c_str());
}

bool processConfigurationMessage(const String& payload) {
  DEBUG_PRINTF("Processing configuration message: %s\n", payload.c_str());
  
  StaticJsonDocument<2048> doc;
  DeserializationError error = deserializeJson(doc, payload);
  
  if (error) {
    DEBUG_PRINTF("JSON parsing failed: %s\n", error.c_str());
    return false;
  }
  
  // Store original config for comparison
  device_config_t originalConfig = config;
  bool configChanged = false;
  
  // Process network configuration
  if (doc.containsKey("wifi_ssid")) {
    String newSsid = doc["wifi_ssid"].as<String>();
    if (newSsid != String(config.wifi_ssid)) {
      strncpy(config.wifi_ssid, newSsid.c_str(), sizeof(config.wifi_ssid) - 1);
      config.wifi_ssid[sizeof(config.wifi_ssid) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTF("Updated WiFi SSID: %s\n", config.wifi_ssid);
    }
  }
  
  if (doc.containsKey("wifi_pass")) {
    String newPass = doc["wifi_pass"].as<String>();
    if (newPass != String(config.wifi_pass)) {
      strncpy(config.wifi_pass, newPass.c_str(), sizeof(config.wifi_pass) - 1);
      config.wifi_pass[sizeof(config.wifi_pass) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTLN("Updated WiFi password");
    }
  }
  
  // Process MQTT configuration
  if (doc.containsKey("mqtt_endpoint")) {
    String newEndpoint = doc["mqtt_endpoint"].as<String>();
    if (newEndpoint != String(config.mqtt_endpoint)) {
      strncpy(config.mqtt_endpoint, newEndpoint.c_str(), sizeof(config.mqtt_endpoint) - 1);
      config.mqtt_endpoint[sizeof(config.mqtt_endpoint) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTF("Updated MQTT endpoint: %s\n", config.mqtt_endpoint);
    }
  }
  
  if (doc.containsKey("mqtt_port")) {
    uint16_t newPort = doc["mqtt_port"].as<uint16_t>();
    if (newPort != config.mqtt_port) {
      config.mqtt_port = newPort;
      configChanged = true;
      DEBUG_PRINTF("Updated MQTT port: %d\n", config.mqtt_port);
    }
  }
  
  // Process certificates
  if (doc.containsKey("mqtt_cert")) {
    String newCert = doc["mqtt_cert"].as<String>();
    if (newCert != String(config.mqtt_cert)) {
      strncpy(config.mqtt_cert, newCert.c_str(), sizeof(config.mqtt_cert) - 1);
      config.mqtt_cert[sizeof(config.mqtt_cert) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTLN("Updated MQTT certificate");
    }
  }
  
  if (doc.containsKey("mqtt_key")) {
    String newKey = doc["mqtt_key"].as<String>();
    if (newKey != String(config.mqtt_key)) {
      strncpy(config.mqtt_key, newKey.c_str(), sizeof(config.mqtt_key) - 1);
      config.mqtt_key[sizeof(config.mqtt_key) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTLN("Updated MQTT private key");
    }
  }
  
  if (doc.containsKey("mqtt_ca")) {
    String newCa = doc["mqtt_ca"].as<String>();
    if (newCa != String(config.mqtt_ca)) {
      strncpy(config.mqtt_ca, newCa.c_str(), sizeof(config.mqtt_ca) - 1);
      config.mqtt_ca[sizeof(config.mqtt_ca) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTLN("Updated MQTT CA certificate");
    }
  }
  
  // Process publishing configuration
  if (doc.containsKey("publish_interval_sec")) {
    uint32_t newInterval = doc["publish_interval_sec"].as<uint32_t>();
    if (newInterval != config.publish_interval_sec) {
      config.publish_interval_sec = newInterval;
      configChanged = true;
      DEBUG_PRINTF("Updated publish interval: %d sec\n", config.publish_interval_sec);
    }
  }
  
  if (doc.containsKey("publish_regardless_of_change")) {
    bool newValue = doc["publish_regardless_of_change"].as<bool>();
    if (newValue != config.publish_regardless_of_change) {
      config.publish_regardless_of_change = newValue;
      configChanged = true;
      DEBUG_PRINTF("Updated publish regardless: %s\n", newValue ? "true" : "false");
    }
  }
  
  // Process UI configuration
  if (doc.containsKey("ui_name")) {
    String newName = doc["ui_name"].as<String>();
    if (newName != String(config.ui_name)) {
      strncpy(config.ui_name, newName.c_str(), sizeof(config.ui_name) - 1);
      config.ui_name[sizeof(config.ui_name) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTF("Updated UI name: %s\n", config.ui_name);
    }
  }
  
  // Process sensor calibration
  if (doc.containsKey("adc_offset")) {
    float newOffset = doc["adc_offset"].as<float>();
    if (abs(newOffset - config.adc_offset) > 0.001f) {
      config.adc_offset = newOffset;
      configChanged = true;
      DEBUG_PRINTF("Updated ADC offset: %.3f\n", config.adc_offset);
    }
  }
  
  if (doc.containsKey("temp_offset")) {
    float newOffset = doc["temp_offset"].as<float>();
    if (abs(newOffset - config.temp_offset) > 0.001f) {
      config.temp_offset = newOffset;
      configChanged = true;
      DEBUG_PRINTF("Updated temp offset: %.3f\n", config.temp_offset);
    }
  }
  
  if (doc.containsKey("unit_temp")) {
    String newUnit = doc["unit_temp"].as<String>();
    char newTempUnit = newUnit.length() > 0 ? newUnit.charAt(0) : 'C';
    if (newTempUnit != config.temp_unit) {
      config.temp_unit = newTempUnit;
      configChanged = true;
      DEBUG_PRINTF("Updated temp unit: %c\n", config.temp_unit);
    }
  }
  
  // Process binary sensor labels
  if (doc.containsKey("bin1_label_on")) {
    String newLabel = doc["bin1_label_on"].as<String>();
    if (newLabel != String(config.bin1_label_on)) {
      strncpy(config.bin1_label_on, newLabel.c_str(), sizeof(config.bin1_label_on) - 1);
      config.bin1_label_on[sizeof(config.bin1_label_on) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTF("Updated bin1 on label: %s\n", config.bin1_label_on);
    }
  }
  
  if (doc.containsKey("bin1_label_off")) {
    String newLabel = doc["bin1_label_off"].as<String>();
    if (newLabel != String(config.bin1_label_off)) {
      strncpy(config.bin1_label_off, newLabel.c_str(), sizeof(config.bin1_label_off) - 1);
      config.bin1_label_off[sizeof(config.bin1_label_off) - 1] = '\0';
      configChanged = true;
      DEBUG_PRINTF("Updated bin1 off label: %s\n", config.bin1_label_off);
    }
  }
  
  // Process display configuration
  if (doc.containsKey("display_decimal_places")) {
    uint8_t newPlaces = doc["display_decimal_places"].as<uint8_t>();
    if (newPlaces != config.adc_decimal_places) {
      config.adc_decimal_places = newPlaces;
      config.temp_decimal_places = newPlaces;
      config.hum_decimal_places = newPlaces;
      configChanged = true;
      DEBUG_PRINTF("Updated decimal places: %d\n", newPlaces);
    }
  }
  
  // Save configuration if changed
  if (configChanged) {
    DEBUG_PRINTLN("Configuration updated, saving to EEPROM...");
    saveConfigurationToEEPROM();
    
    // Notify lifecycle manager
    handleConfigurationReceived();
    
    // Publish updated hello message
    publishHelloMessage();
    
    DEBUG_PRINTLN("Configuration update complete. Rebooting in 5 seconds...");
    delay(5000);
    ESP.restart();
  } else {
    DEBUG_PRINTLN("No configuration changes detected");
  }
  
  return configChanged;
}

bool validateConfiguration() {
  // Basic validation checks
  bool valid = true;
  
  // Check required fields
  if (strlen(config.device_id) == 0) {
    DEBUG_PRINTLN("Validation failed: missing device ID");
    valid = false;
  }
  
  if (strlen(config.wifi_ssid) == 0) {
    DEBUG_PRINTLN("Validation failed: missing WiFi SSID");
    valid = false;
  }
  
  if (strlen(config.mqtt_endpoint) == 0) {
    DEBUG_PRINTLN("Validation failed: missing MQTT endpoint");
    valid = false;
  }
  
  if (config.mqtt_port == 0) {
    DEBUG_PRINTLN("Validation failed: invalid MQTT port");
    valid = false;
  }
  
  if (config.publish_interval_sec == 0) {
    DEBUG_PRINTLN("Validation failed: invalid publish interval");
    valid = false;
  }
  
  // Check ranges
  if (config.temp_unit != 'C' && config.temp_unit != 'F') {
    DEBUG_PRINTF("Validation warning: invalid temp unit '%c', using 'C'\n", config.temp_unit);
    config.temp_unit = 'C';
  }
  
  if (config.adc_decimal_places > 3) {
    DEBUG_PRINTF("Validation warning: decimal places %d > 3, using 3\n", config.adc_decimal_places);
    config.adc_decimal_places = 3;
  }
  
  return valid;
}
