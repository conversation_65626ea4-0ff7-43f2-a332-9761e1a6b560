# Type101Sensor_AWS - AWS IoT Core Version

ESP8266 NodeMCU sensor device with AM2320 temperature/humidity sensor, OLED display, and AWS IoT Core connectivity.

## 🎯 **Project Overview**

This is the **AWS IoT Core version** of the Type101Sensor project, based on the working MQTT version but modified for secure AWS IoT connectivity.

### **📋 Features**

#### ✅ **Implemented**
- **ESP8266 NodeMCU v1** compatibility
- **AM2320 temperature/humidity sensor** reading
- **ADC voltage measurement** (0-3.3V)
- **Digital binary inputs** on D5/D6 pins
- **128x32 OLED display** with status icons
- **WiFi configuration portal** for easy setup
- **EEPROM configuration storage**
- **NTP time synchronization** for ISO 8601 timestamps
- **JSON telemetry payload** generation
- **Unique device identification** via chip ID

#### ✅ **Fully Implemented**
- **AWS IoT Core MQTT over TLS** connection with certificates
- **Telemetry publishing** to AWS IoT Core
- **Configuration topic subscription** and message processing
- **Control topic subscription** (framework ready)

#### 🚧 **Partially Implemented**
- **Control message processing** (subscribed but handlers not implemented)

### **🔧 Hardware Requirements**
- **Microcontroller**: ESP8266 NodeMCU v1.0 (ESP-12F) or Wemos D1 Mini 
- **Sensors**:
  - AM2320 I²C temperature and humidity sensor
  - ESP8266 ADC0 pin for analog voltage (0–3.3V)
  - Digital binary inputs on pins D5 (GPIO14) and D6 (GPIO12) for the ESP8266 boards or 
- **Display**: SSD1306 OLED (128x32, via I²C)

### **📡 Pin Connections**
```
NodeMCU v1/D1mini Component   
------------- | ---------
D1 (GPIO5)    | I2C SCL (AM2320 & OLED)
D2 (GPIO4)    | I2C SDA (AM2320 & OLED)
D5 (GPIO14)   | Digital binary input (LSB)
D6 (GPIO12)   | Digital binary input (MSB)
A0            | Analog voltage input (0-3.3V)
3V3           | VCC (AM2320 & OLED)
GND           | GND (AM2320 & OLED)
```

### **🚀 Getting Started**

#### **1. AWS IoT Setup**
Before using this code, you need to:
1. Create an AWS IoT Thing in the AWS Console
2. Generate device certificates
3. Create IoT policies
4. Replace the certificate placeholders in `main.cpp`

#### **2. PlatformIO Setup**
```bash
# Clone or copy this project
cd Type101Sensor_AWS

# Install dependencies (PlatformIO will handle this)
platformio lib install

# Build the project
platformio run

# Upload to device
platformio run --target upload
```


### **📊 AWS IoT Topics (POC Configuration)**

For the POC phase, the device emulates the existing Raspberry Pi aggregator `adcbc1`:
- **AWS IoT Thing Name:** `pi_aggregator_adcbc1`
- **Telemetry Publishing:** `sensors/pi_aggregator_adcbc1/readings`
- **Configuration:** `config/pi_aggregator_adcbc1/set`
- **Control:** `control/pi_aggregator_adcbc1/#`

**Note:** Future versions will use device UID-based naming, but for POC testing, this matches the existing aggregator infrastructure.

### **🔒 Security**

This version uses:
- **TLS 1.2** encryption for all communications
- **X.509 certificates** for device authentication
- **AWS IoT policies** for access control
- **Device shadows** for secure configuration updates

### **📈 Telemetry Data Format**

Sensor data is published to topic `sensors/pi_aggregator_adcbc1/readings` in JSON format:
```json
{
  "temperature": 74.2,
  "humidity": 61.4,
  "voltage": 3.28,
  "binary": 2,
  "timestamp": "2025-06-15T01:23:45Z"
}
```

**Field Descriptions:**
- `temperature`: Temperature in Celsius (float, 1 decimal place)
- `humidity`: Relative humidity percentage (float, 1 decimal place)
- `voltage`: ADC voltage reading 0-3.3V (float, 2 decimal places)
- `binary`: 2-bit value from D5/D6 pins (D6=MSB, D5=LSB, range 0-3)
- `timestamp`: ISO 8601 UTC timestamp

### **🔧 Configuration**

Device configuration can be updated via the config topic `config/pi_aggregator_adcbc1/set`:
```json
{
  "interval": 30
}
```

**Configuration Parameters:**
- `interval`: Telemetry publishing interval in seconds (5-3600)

### **📝 Version History**

- **v1.0-AWS**: Initial AWS IoT Core version based on MQTT v0.7
- Based on working MQTT version with 937 lines of code
- Added AWS IoT Core connectivity with TLS/SSL
- Added device shadow support
- Embedded icons for standalone operation


### **🧪 Testing and Deployment**



### **⚠️ Important Notes**

1. **POC Configuration**: Device emulates existing `pi_aggregator_adcbc1` thing for testing
2. **Real Certificates**: Uses actual AWS IoT certificates and endpoint (not placeholders)
3. **Topic Structure**: Publishes to `sensors/pi_aggregator_adcbc1/readings` matching existing infrastructure
4. **Memory Usage**: AWS IoT requires more memory than basic MQTT due to TLS overhead
5. **Future Enhancement**: Production version will use device UID-based naming

