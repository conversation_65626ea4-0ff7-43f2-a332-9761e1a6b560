// ESP8266 AWS IoT MINIMAL TEST - Stripped down to bare essentials
// Goal: Determine if <PERSON><PERSON><PERSON>266 can handle AWS IoT Core at all
// Removed: OLED display, AM2320 sensor, EEPROM, config portal, NTP
// Kept: Only WiFi + AWS IoT Core + basic telemetry

#include <ESP8266WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>
#include <ArduinoJson.h>

// === WIFI CONFIGURATION ===
#define WIFI_SSID "HaciendaHagansLR"
#define WIFI_PASS "F1nglongers"

// === AWS IOT CORE CONFIGURATION ===
const char* aws_endpoint = "a2ltfva6jq44zv-ats.iot.us-east-1.amazonaws.com";
const int aws_port = 8883;
const char* client_id = "minimal_test_esp8266";
const char* telemetry_topic = "test/minimal/telemetry";

// === AWS IOT CORE CERTIFICATES ===
static const char certificate_pem_crt[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDWTCCAkGgAwIBAgIUQLLrIO5d6G9m6/tgpMttQs9ncW0wDQYJKoZIhvcNAQEL
BQAwTTFLMEkGA1UECwxCQW1hem9uIFdlYiBTZXJ2aWNlcyBPPUFtYXpvbi5jb20g
SW5jLiBMPVNlYXR0bGUgU1Q9V2FzaGluZ3RvbiBDPVVTMB4XDTI1MDYxMzAwNTgx
NVoXDTQ5MTIzMTIzNTk1OVowHjEcMBoGA1UEAwwTQVdTIElvVCBDZXJ0aWZpY2F0
ZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKsW39KzechA5NkWWfAF
5H6sNeICnc9hjGjZQ3tI6VSHjDFhGNtnf+2eC6HQZ/m86jSOtmJnvYD0lBd/4cle
ZT/LLiDTRgyFL+GKnmgRN0KhNsvgVaR1tnTMY+AszAhpgBf/AHTgoq57d3mG4y22
jXqa8iZmHm4y1tm60/b44gdHbVEdRko1ntCoaOTKqMOJhIWMMO6EjfawcpNwb3Pu
v5PWDGyqWsPEA9ZJ74WZNVAcZkY0ei9jF/lyzJGiNjEKgduQshac4EjZRchFDCZi
5fYmWVyKXn8DZAWxA03ng6pFI/OajcSvSVGa9tTsLb0H1wVvZqX4x7k3u2F3uoe1
Nd0CAwEAAaNgMF4wHwYDVR0jBBgwFoAUqHWRrqPVtVv1vCaOqsZ0Z00nZ24wHQYD
VR0OBBYEFLgIoaMa8XrekF2uZJEgiGTR5NDJMAwGA1UdEwEB/wQCMAAwDgYDVR0P
AQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCg7mMDsgkI33zT4pBQwC8Yyyxs
uwEuMnZu0ekJdWx7UL2aEXc7zyFNDPYEilE3CF7UyQ3D0yn43vIN+jKNSHVtnVWK
Np1B6aT0x2Cd1PDsi0OOoFuhxkQ1evLbfrEh9ECJOYRd3ll2v0yPOpTDAN/nHEOy
tbbRvbt4lwYQagahz+tJcyv0LsZj5rY/m5bDKRTcWj0IdGKF2Y6fc616AvJf/G3s
kHYSGXsOzJJ4gwENzYiGmIS9m6RmSjrwu/zy2dsJ3VHgqmKgeVOaQEkr1Zz4L47m
MlGKkBLCUcmTfc8v2fUf6YVPXwgLjVpzvZ2OU1u5AVMUPat7r16d2Xcy0O76
-----END CERTIFICATE-----
)EOF";

static const char private_pem_key[] PROGMEM = R"EOF(
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)EOF";

static const char amazon_ca_cert[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
ADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6
b24gUm9vdCBDQSAxMB4XDTE1MDUyNjAwMDAwMFoXDTM4MDExNzAwMDAwMFowOTEL
MAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv
b3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj
ca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM
9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw
IFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6
VOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L
93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm
jgSubJrIqg0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0OBBYEFIQYzIU07LwMlJQuCFmcx7IQTgoIMA0GCSqGSIb3DQEBCwUA
A4IBAQCY8jdaQZChGsV2USggNiMOruYou6r4lK5IpDB/G/wkjUu0yKGX9rbxenDI
U5PMCCjjmCXPI6T53iHTfIUJrU6adTrCC2qJeHZERxhlbI1Bjjt/msv0tadQ1wUs
N+gDS63pYaACbvXy8MWy7Vu33PqUXHeeE6V/Uq2V8viTO96LXFvKWlJbYK8U90vv
o/ufQJVtMVT8QtPHRh8jrdkPSHCa2XV4cdFyQzR1bldZwgJcJmApzyMZFo6IQ6XU
5MsI+yMRQ+hDKXJioaldXgjUkK642M4UwtBV8ob2xJNDd2ZhwLnoQdeXeGADbkpy
rqXRfboQnoZsG4q5WTP468SQvvG5
-----END CERTIFICATE-----
)EOF";

// === OBJECTS ===
BearSSL::WiFiClientSecure awsClient;
PubSubClient mqttClient(awsClient);

// === GLOBAL VARIABLES ===
bool awsConnected = false;
unsigned long lastPublishTime = 0;
unsigned long lastConnectAttempt = 0;
const unsigned long PUBLISH_INTERVAL = 30000; // 30 seconds
const unsigned long CONNECT_RETRY_INTERVAL = 10000; // 10 seconds

// === FUNCTION PROTOTYPES ===
void setupWiFi();
void setupAWS();
void connectAWS();
void publishTelemetry();
void mqttCallback(char* topic, byte* payload, unsigned int length);

void setup() {
  Serial.begin(115200);
  delay(1000);
  
  Serial.println("\n=== ESP8266 AWS IoT MINIMAL TEST ===");
  Serial.print("Free heap at start: "); Serial.println(ESP.getFreeHeap());
  Serial.print("ESP8266 Core version: "); Serial.println(ESP.getCoreVersion());
  
  setupWiFi();
  if (WiFi.status() == WL_CONNECTED) {
    setupAWS();
  }
  
  Serial.println("Setup complete. Starting main loop...");
}

void loop() {
  // Feed watchdog
  ESP.wdtFeed();
  
  // Try to connect to AWS if not connected
  if (WiFi.status() == WL_CONNECTED && !awsConnected) {
    unsigned long now = millis();
    if (now - lastConnectAttempt > CONNECT_RETRY_INTERVAL) {
      connectAWS();
      lastConnectAttempt = now;
    }
  }
  
  // Process MQTT messages
  if (awsConnected) {
    mqttClient.loop();
  }
  
  // Publish telemetry periodically
  unsigned long now = millis();
  if (awsConnected && (now - lastPublishTime > PUBLISH_INTERVAL)) {
    publishTelemetry();
    lastPublishTime = now;
  }
  
  // Print status every 10 seconds
  static unsigned long lastStatus = 0;
  if (now - lastStatus > 10000) {
    Serial.print("Status - WiFi: "); Serial.print(WiFi.status() == WL_CONNECTED ? "OK" : "FAIL");
    Serial.print(", AWS: "); Serial.print(awsConnected ? "OK" : "FAIL");
    Serial.print(", Heap: "); Serial.println(ESP.getFreeHeap());
    lastStatus = now;
  }
  
  delay(100); // Small delay to prevent tight loop
}

void setupWiFi() {
  Serial.print("Connecting to WiFi: "); Serial.println(WIFI_SSID);
  WiFi.mode(WIFI_STA);
  WiFi.begin(WIFI_SSID, WIFI_PASS);

  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 30) {
    delay(500);
    Serial.print(".");
    attempts++;
    ESP.wdtFeed();
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\nWiFi connected!");
    Serial.print("IP: "); Serial.println(WiFi.localIP());
    Serial.print("RSSI: "); Serial.println(WiFi.RSSI());
  } else {
    Serial.println("\nWiFi connection failed!");
  }
}

void setupAWS() {
  Serial.println("Setting up AWS IoT...");
  Serial.print("Free heap before AWS setup: "); Serial.println(ESP.getFreeHeap());

  // Load certificates with minimal memory usage
  BearSSL::X509List cert((const uint8_t*)certificate_pem_crt, strlen_P(certificate_pem_crt));
  BearSSL::X509List ca((const uint8_t*)amazon_ca_cert, strlen_P(amazon_ca_cert));
  BearSSL::PrivateKey key((const uint8_t*)private_pem_key, strlen_P(private_pem_key));

  // Configure BearSSL with minimal settings
  awsClient.setTrustAnchors(&ca);
  awsClient.setClientRSACert(&cert, &key);
  awsClient.setBufferSizes(256, 256);  // Minimal buffer sizes
  awsClient.setTimeout(5000);          // Short timeout

  // Configure MQTT client
  mqttClient.setServer(aws_endpoint, aws_port);
  mqttClient.setCallback(mqttCallback);

  Serial.print("Free heap after AWS setup: "); Serial.println(ESP.getFreeHeap());
  Serial.println("AWS IoT setup complete.");
}

void connectAWS() {
  if (mqttClient.connected()) {
    awsConnected = true;
    return;
  }

  Serial.print("Attempting AWS connection... Heap: "); Serial.println(ESP.getFreeHeap());
  ESP.wdtFeed();

  // Try to connect with timeout
  bool connected = mqttClient.connect(client_id);
  ESP.wdtFeed();

  if (connected) {
    Serial.println("AWS IoT connected!");
    awsConnected = true;
  } else {
    Serial.print("AWS connection failed. State: ");
    Serial.println(mqttClient.state());
    awsConnected = false;
  }
}

void publishTelemetry() {
  if (!mqttClient.connected()) {
    awsConnected = false;
    return;
  }

  ESP.wdtFeed();

  // Create minimal JSON payload
  StaticJsonDocument<200> doc;
  doc["timestamp"] = millis();
  doc["heap"] = ESP.getFreeHeap();
  doc["rssi"] = WiFi.RSSI();
  doc["uptime"] = millis() / 1000;

  String payload;
  serializeJson(doc, payload);

  Serial.print("Publishing: "); Serial.println(payload);

  bool published = mqttClient.publish(telemetry_topic, payload.c_str());
  if (published) {
    Serial.println("Telemetry published successfully!");
  } else {
    Serial.println("Failed to publish telemetry!");
  }

  ESP.wdtFeed();
}

void mqttCallback(char* topic, byte* payload, unsigned int length) {
  Serial.print("Message received ["); Serial.print(topic); Serial.print("]: ");
  for (unsigned int i = 0; i < length; i++) {
    Serial.print((char)payload[i]);
  }
  Serial.println();
}
