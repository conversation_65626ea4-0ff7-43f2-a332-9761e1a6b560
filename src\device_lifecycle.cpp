/*
 * Device Lifecycle Management
 * 
 * Handles device status transitions and lifecycle logic:
 * POC -> <PERSON><PERSON> -> <PERSON><PERSON> -> Child -> Teenager -> Adult -> Geezer
 */

#include <Arduino.h>
#include "config.h"

// External references
extern device_config_t config;
extern uint8_t connectionFailures;
extern unsigned long lastConnectionAttempt;

// Function prototypes
void saveConfigurationToEEPROM();

// Status transition tracking
static device_status_t previousStatus = STATUS_NEWBORN;
static unsigned long statusChangeTime = 0;
static bool configurationReceived = false;

const char* getStatusName(device_status_t status) {
  switch (status) {
    case STATUS_POC: return "POC";
    case STATUS_NEWBORN: return "Newborn";
    case STATUS_TODDLER: return "Toddler";
    case STATUS_CHILD: return "Child";
    case STATUS_TEENAGER: return "Teenager";
    case STATUS_ADULT: return "Adult";
    case STATUS_GEEZER: return "Geezer";
    default: return "Unknown";
  }
}

void initializeLifecycle() {
  DEBUG_PRINTLN("Initializing device lifecycle...");
  
  previousStatus = config.status_code;
  statusChangeTime = millis();
  
  DEBUG_PRINTF("Initial status: %s (%d)\n", 
               getStatusName(config.status_code), config.status_code);
}

bool canTransitionTo(device_status_t newStatus) {
  device_status_t current = config.status_code;
  
  // POC mode can transition to any status
  if (current == STATUS_POC) {
    return true;
  }
  
  // Valid transitions based on lifecycle
  switch (current) {
    case STATUS_NEWBORN:
      return (newStatus == STATUS_TODDLER || newStatus == STATUS_GEEZER);
      
    case STATUS_TODDLER:
      return (newStatus == STATUS_CHILD || newStatus == STATUS_TEENAGER || 
              newStatus == STATUS_NEWBORN || newStatus == STATUS_GEEZER);
      
    case STATUS_CHILD:
      return (newStatus == STATUS_TEENAGER || newStatus == STATUS_TODDLER ||
              newStatus == STATUS_GEEZER);
      
    case STATUS_TEENAGER:
      return (newStatus == STATUS_ADULT || newStatus == STATUS_CHILD ||
              newStatus == STATUS_GEEZER);
      
    case STATUS_ADULT:
      return (newStatus == STATUS_GEEZER || newStatus == STATUS_CHILD);
      
    case STATUS_GEEZER:
      return (newStatus == STATUS_ADULT || newStatus == STATUS_TEENAGER ||
              newStatus == STATUS_NEWBORN);
      
    default:
      return false;
  }
}

void transitionToStatus(device_status_t newStatus, const char* reason) {
  if (config.status_code == newStatus) {
    return; // No change needed
  }
  
  if (!canTransitionTo(newStatus)) {
    DEBUG_PRINTF("Invalid transition from %s to %s\n", 
                 getStatusName(config.status_code), getStatusName(newStatus));
    return;
  }
  
  DEBUG_PRINTF("Status transition: %s -> %s (%s)\n", 
               getStatusName(config.status_code), getStatusName(newStatus), 
               reason ? reason : "no reason");
  
  previousStatus = config.status_code;
  config.status_code = newStatus;
  statusChangeTime = millis();
  
  // Save configuration after status change
  saveConfigurationToEEPROM();
  
  // Reset connection failures on successful transition
  if (newStatus == STATUS_ADULT) {
    connectionFailures = 0;
  }
}

void handleConnectionFailure() {
  connectionFailures++;
  lastConnectionAttempt = millis();
  
  DEBUG_PRINTF("Connection failure #%d (max: %d)\n", 
               connectionFailures, config.max_connection_failures);
  
  if (connectionFailures >= config.max_connection_failures) {
    DEBUG_PRINTLN("Max connection failures reached, handling fallback...");
    
    switch (config.status_code) {
      case STATUS_ADULT:
        // Adult failed to connect to AWS, credentials may be expired
        transitionToStatus(STATUS_GEEZER, "AWS connection failed");
        break;
        
      case STATUS_TEENAGER:
        // Teenager failed to connect, may need reprovisioning
        transitionToStatus(STATUS_CHILD, "Connection verification failed");
        break;
        
      case STATUS_CHILD:
        // Child failed, back to partial config state
        transitionToStatus(STATUS_TODDLER, "Connection issues persist");
        break;
        
      case STATUS_TODDLER:
        // Toddler failed, may need complete reprovisioning
        transitionToStatus(STATUS_NEWBORN, "Provisioning connection failed");
        break;
        
      case STATUS_GEEZER:
        // Geezer failed to connect to provisioning, stay in geezer
        DEBUG_PRINTLN("Geezer mode: staying in current status");
        break;
        
      case STATUS_NEWBORN:
        // Newborn failed, stay newborn but log issue
        DEBUG_PRINTLN("Newborn mode: staying in current status");
        break;
        
      case STATUS_POC:
        // POC mode failure - transition to geezer for credential renewal
        transitionToStatus(STATUS_GEEZER, "POC mode connection failed");
        break;
        
      default:
        DEBUG_PRINTF("Unhandled status in failure: %d\n", config.status_code);
        break;
    }
    
    // Reset failure counter after handling
    connectionFailures = 0;
  }
}

void handleConfigurationReceived() {
  configurationReceived = true;
  
  DEBUG_PRINTLN("Configuration received, updating status...");
  
  switch (config.status_code) {
    case STATUS_NEWBORN:
      transitionToStatus(STATUS_TEENAGER, "Initial configuration received");
      break;
      
    case STATUS_TODDLER:
      transitionToStatus(STATUS_TEENAGER, "Configuration updated");
      break;
      
    case STATUS_CHILD:
      transitionToStatus(STATUS_TEENAGER, "Configuration corrected");
      break;
      
    case STATUS_GEEZER:
      transitionToStatus(STATUS_ADULT, "Credentials renewed");
      break;
      
    default:
      DEBUG_PRINTF("Configuration received in status %s\n", 
                   getStatusName(config.status_code));
      break;
  }
}

void handleSuccessfulConnection() {
  connectionFailures = 0;
  
  DEBUG_PRINTF("Successful connection in status %s\n", 
               getStatusName(config.status_code));
  
  switch (config.status_code) {
    case STATUS_TEENAGER:
      transitionToStatus(STATUS_ADULT, "Connection verified");
      break;
      
    case STATUS_GEEZER:
      // Geezer connected successfully, but may need new credentials
      DEBUG_PRINTLN("Geezer connected - awaiting credential renewal");
      break;
      
    default:
      // Other statuses don't auto-transition on connection
      break;
  }
}

bool shouldRetryConnection() {
  unsigned long now = millis();
  unsigned long timeSinceLastAttempt = now - lastConnectionAttempt;
  
  // Calculate retry interval with jitter
  unsigned long baseInterval = config.connection_retry_interval_ms;
  unsigned long jitter = random(0, PROVISIONING_JITTER_MAX);
  unsigned long retryInterval = baseInterval + jitter;
  
  // For publishing interval, use the longer of the two
  unsigned long publishInterval = config.publish_interval_sec * 1000;
  if (publishInterval > retryInterval) {
    retryInterval = publishInterval;
  }
  
  return (timeSinceLastAttempt >= retryInterval);
}

bool isOperationalStatus() {
  return (config.status_code == STATUS_ADULT || config.status_code == STATUS_POC);
}

bool isProvisioningStatus() {
  return (config.status_code == STATUS_NEWBORN || 
          config.status_code == STATUS_TODDLER ||
          config.status_code == STATUS_CHILD ||
          config.status_code == STATUS_TEENAGER ||
          config.status_code == STATUS_GEEZER);
}

bool needsProvisioning() {
  return (config.status_code == STATUS_NEWBORN || 
          config.status_code == STATUS_TODDLER ||
          config.status_code == STATUS_GEEZER);
}

bool needsVerification() {
  return (config.status_code == STATUS_TEENAGER);
}

bool hasIssues() {
  return (config.status_code == STATUS_CHILD || 
          config.status_code == STATUS_GEEZER);
}

unsigned long getTimeSinceStatusChange() {
  return millis() - statusChangeTime;
}

device_status_t getPreviousStatus() {
  return previousStatus;
}

void printLifecycleStatus() {
  DEBUG_PRINTLN("\n===== LIFECYCLE STATUS =====");
  DEBUG_PRINTF("Current: %s (%d)\n", getStatusName(config.status_code), config.status_code);
  DEBUG_PRINTF("Previous: %s (%d)\n", getStatusName(previousStatus), previousStatus);
  DEBUG_PRINTF("Time in status: %lu ms\n", getTimeSinceStatusChange());
  DEBUG_PRINTF("Connection failures: %d/%d\n", connectionFailures, config.max_connection_failures);
  DEBUG_PRINTF("Operational: %s\n", isOperationalStatus() ? "Yes" : "No");
  DEBUG_PRINTF("Needs provisioning: %s\n", needsProvisioning() ? "Yes" : "No");
  DEBUG_PRINTF("Needs verification: %s\n", needsVerification() ? "Yes" : "No");
  DEBUG_PRINTF("Has issues: %s\n", hasIssues() ? "Yes" : "No");
  DEBUG_PRINTLN("============================\n");
}
