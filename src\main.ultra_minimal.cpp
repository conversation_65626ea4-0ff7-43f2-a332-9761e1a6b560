// ESP8266 AWS IoT ULTRA MINIMAL TEST - Maximum memory optimization
// Alternative approaches to reduce memory usage:
// 1. Smallest possible buffer sizes
// 2. No JSON library (manual string building)
// 3. Minimal certificate validation
// 4. Single connection attempt
// 5. No MQTT callback (publish only)

#include <ESP8266WiFi.h>
#include <WiFiClientSecure.h>
#include <PubSubClient.h>

// === WIFI CONFIGURATION ===
#define WIFI_SSID "HaciendaHagansLR"
#define WIFI_PASS "F1nglongers"

// === AWS IOT CORE CONFIGURATION ===
const char* aws_endpoint = "a2ltfva6jq44zv-ats.iot.us-east-1.amazonaws.com";
const int aws_port = 8883;
const char* client_id = "ultra_minimal_esp8266";
const char* telemetry_topic = "test/ultra_minimal/telemetry";

// === AWS IOT CORE CERTIFICATES ===
static const char certificate_pem_crt[] PROGMEM = R"EOF(
-----B<PERSON><PERSON> CERTIFICATE-----
MIIDWTCCAkGgAwIBAgIUQLLrIO5d6G9m6/tgpMttQs9ncW0wDQYJKoZIhvcNAQEL
BQAwTTFLMEkGA1UECwxCQW1hem9uIFdlYiBTZXJ2aWNlcyBPPUFtYXpvbi5jb20g
SW5jLiBMPVNlYXR0bGUgU1Q9V2FzaGluZ3RvbiBDPVVTMB4XDTI1MDYxMzAwNTgx
NVoXDTQ5MTIzMTIzNTk1OVowHjEcMBoGA1UEAwwTQVdTIElvVCBDZXJ0aWZpY2F0
ZTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAKsW39KzechA5NkWWfAF
5H6sNeICnc9hjGjZQ3tI6VSHjDFhGNtnf+2eC6HQZ/m86jSOtmJnvYD0lBd/4cle
ZT/LLiDTRgyFL+GKnmgRN0KhNsvgVaR1tnTMY+AszAhpgBf/AHTgoq57d3mG4y22
jXqa8iZmHm4y1tm60/b44gdHbVEdRko1ntCoaOTKqMOJhIWMMO6EjfawcpNwb3Pu
v5PWDGyqWsPEA9ZJ74WZNVAcZkY0ei9jF/lyzJGiNjEKgduQshac4EjZRchFDCZi
5fYmWVyKXn8DZAWxA03ng6pFI/OajcSvSVGa9tTsLb0H1wVvZqX4x7k3u2F3uoe1
Nd0CAwEAAaNgMF4wHwYDVR0jBBgwFoAUqHWRrqPVtVv1vCaOqsZ0Z00nZ24wHQYD
VR0OBBYEFLgIoaMa8XrekF2uZJEgiGTR5NDJMAwGA1UdEwEB/wQCMAAwDgYDVR0P
AQH/BAQDAgeAMA0GCSqGSIb3DQEBCwUAA4IBAQCg7mMDsgkI33zT4pBQwC8Yyyxs
uwEuMnZu0ekJdWx7UL2aEXc7zyFNDPYEilE3CF7UyQ3D0yn43vIN+jKNSHVtnVWK
Np1B6aT0x2Cd1PDsi0OOoFuhxkQ1evLbfrEh9ECJOYRd3ll2v0yPOpTDAN/nHEOy
tbbRvbt4lwYQagahz+tJcyv0LsZj5rY/m5bDKRTcWj0IdGKF2Y6fc616AvJf/G3s
kHYSGXsOzJJ4gwENzYiGmIS9m6RmSjrwu/zy2dsJ3VHgqmKgeVOaQEkr1Zz4L47m
MlGKkBLCUcmTfc8v2fUf6YVPXwgLjVpzvZ2OU1u5AVMUPat7r16d2Xcy0O76
-----END CERTIFICATE-----
)EOF";

static const char private_pem_key[] PROGMEM = R"EOF(
*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************)EOF";

static const char amazon_ca_cert[] PROGMEM = R"EOF(
-----BEGIN CERTIFICATE-----
MIIDQTCCAimgAwIBAgITBmyfz5m/jAo54vB4ikPmljZbyjANBgkqhkiG9w0BAQsF
ADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6
b24gUm9vdCBDQSAxMB4XDTE1MDUyNjAwMDAwMFoXDTM4MDExNzAwMDAwMFowOTEL
MAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv
b3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj
ca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM
9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw
IFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6
VOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L
93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm
jgSubJrIqg0CAwEAAaNCMEAwDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMC
AYYwHQYDVR0OBBYEFIQYzIU07LwMlJQuCFmcx7IQTgoIMA0GCSqGSIb3DQEBCwUA
A4IBAQCY8jdaQZChGsV2USggNiMOruYou6r4lK5IpDB/G/wkjUu0yKGX9rbxenDI
U5PMCCjjmCXPI6T53iHTfIUJrU6adTrCC2qJeHZERxhlbI1Bjjt/msv0tadQ1wUs
N+gDS63pYaACbvXy8MWy7Vu33PqUXHeeE6V/Uq2V8viTO96LXFvKWlJbYK8U90vv
o/ufQJVtMVT8QtPHRh8jrdkPSHCa2XV4cdFyQzR1bldZwgJcJmApzyMZFo6IQ6XU
5MsI+yMRQ+hDKXJioaldXgjUkK642M4UwtBV8ob2xJNDd2ZhwLnoQdeXeGADbkpy
rqXRfboQnoZsG4q5WTP468SQvvG5
-----END CERTIFICATE-----
)EOF";

// === OBJECTS ===
BearSSL::WiFiClientSecure awsClient;
PubSubClient mqttClient(awsClient);

// === GLOBAL VARIABLES ===
bool connectionAttempted = false;
unsigned long startTime = 0;

void setup() {
  Serial.begin(115200);
  delay(1000);
  startTime = millis();
  
  Serial.println("\n=== ESP8266 AWS IoT ULTRA MINIMAL TEST ===");
  Serial.print("Free heap at start: "); Serial.println(ESP.getFreeHeap());
  
  // Connect to WiFi
  Serial.print("Connecting to WiFi...");
  WiFi.mode(WIFI_STA);
  WiFi.begin(WIFI_SSID, WIFI_PASS);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(500);
    Serial.print(".");
    attempts++;
    ESP.wdtFeed();
  }
  
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("\nWiFi failed! Restarting...");
    ESP.restart();
  }
  
  Serial.println("\nWiFi connected!");
  Serial.print("IP: "); Serial.println(WiFi.localIP());
  Serial.print("Free heap after WiFi: "); Serial.println(ESP.getFreeHeap());
  
  // Setup AWS with ultra minimal configuration
  setupAWSUltraMinimal();
  
  Serial.println("Setup complete.");
}

void loop() {
  ESP.wdtFeed();
  
  // Single connection attempt
  if (!connectionAttempted) {
    attemptAWSConnection();
    connectionAttempted = true;
  }
  
  // Print status every 5 seconds
  static unsigned long lastStatus = 0;
  unsigned long now = millis();
  if (now - lastStatus > 5000) {
    Serial.print("Uptime: "); Serial.print((now - startTime) / 1000);
    Serial.print("s, Heap: "); Serial.print(ESP.getFreeHeap());
    Serial.print(", WiFi: "); Serial.println(WiFi.RSSI());
    lastStatus = now;
  }
  
  delay(1000);
}

void setupAWSUltraMinimal() {
  Serial.println("Setting up AWS with ultra minimal config...");
  Serial.print("Free heap before: "); Serial.println(ESP.getFreeHeap());

  // Load certificates
  BearSSL::X509List cert((const uint8_t*)certificate_pem_crt, strlen_P(certificate_pem_crt));
  BearSSL::X509List ca((const uint8_t*)amazon_ca_cert, strlen_P(amazon_ca_cert));
  BearSSL::PrivateKey key((const uint8_t*)private_pem_key, strlen_P(private_pem_key));

  // Ultra minimal BearSSL configuration
  awsClient.setTrustAnchors(&ca);
  awsClient.setClientRSACert(&cert, &key);
  awsClient.setBufferSizes(128, 128);  // Absolute minimum
  awsClient.setTimeout(3000);          // Very short timeout

  // Configure MQTT
  mqttClient.setServer(aws_endpoint, aws_port);

  Serial.print("Free heap after: "); Serial.println(ESP.getFreeHeap());
}

void attemptAWSConnection() {
  Serial.println("=== ATTEMPTING AWS CONNECTION ===");
  Serial.print("Free heap before connection: "); Serial.println(ESP.getFreeHeap());

  ESP.wdtFeed();

  // Attempt connection
  Serial.println("Starting MQTT connect...");
  bool connected = mqttClient.connect(client_id);

  ESP.wdtFeed();

  if (connected) {
    Serial.println("SUCCESS! AWS IoT connected!");

    // Try to publish one message
    String payload = "{\"test\":\"success\",\"heap\":";
    payload += ESP.getFreeHeap();
    payload += ",\"uptime\":";
    payload += (millis() - startTime) / 1000;
    payload += "}";

    Serial.print("Publishing: "); Serial.println(payload);

    bool published = mqttClient.publish(telemetry_topic, payload.c_str());
    if (published) {
      Serial.println("PUBLISH SUCCESS!");
    } else {
      Serial.println("PUBLISH FAILED!");
    }

    mqttClient.disconnect();
    Serial.println("Disconnected. Test complete.");

  } else {
    Serial.print("CONNECTION FAILED! State: ");
    Serial.println(mqttClient.state());
    Serial.print("Free heap after failure: "); Serial.println(ESP.getFreeHeap());
  }

  ESP.wdtFeed();
}
